import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Check, ArrowRight } from 'lucide-react'

interface PaymentSuccessProps {
  onContinue: () => void
}

export function PaymentSuccess({ onContinue }: PaymentSuccessProps) {
  const [sessionId, setSessionId] = useState<string | null>(null)

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const sessionIdParam = urlParams.get('session_id')
    setSessionId(sessionIdParam)
  }, [])

  return (
    <div className="min-h-screen bg-white">
      {/* Logo */}
      <div className="py-6 px-4">
        <div className="max-w-2xl mx-auto">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-lg flex items-center justify-center">
              <div className="w-4 h-4 bg-white rounded-sm"></div>
            </div>
            <span className="text-2xl font-medium text-emerald-600">LifeSupplier</span>
          </div>
        </div>
      </div>

      <div className="max-w-2xl mx-auto px-4 pb-8">
        {/* Success Message */}
        <div className="text-center mb-12">
          <div className="w-20 h-20 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Check className="w-10 h-10 text-emerald-600" />
          </div>
          <h1 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">Payment Successful!</h1>
          <p className="text-lg text-gray-600">
            Thank you for your purchase. Your health report is now ready to view.
          </p>
        </div>

        {/* Purchase Details */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Purchase Details</h3>
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex justify-between">
                <span>Premium Health Report</span>
                <span>$9.97</span>
              </div>
              <div className="flex justify-between">
                <span>7-Day Full Access</span>
                <span>Included</span>
              </div>
              {sessionId && (
                <div className="flex justify-between text-xs pt-2 border-t">
                  <span>Session ID:</span>
                  <span className="font-mono">{sessionId.slice(-8)}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Continue Button */}
        <Button
          onClick={onContinue}
          className="w-full bg-emerald-600 hover:bg-emerald-700 text-white py-5 md:py-6 text-lg md:text-xl font-bold rounded-xl shadow-lg"
        >
          View My Health Report
          <ArrowRight className="w-5 h-5 ml-2" />
        </Button>

        {/* Additional Info */}
        <div className="mt-8 text-center text-sm text-gray-600">
          <p>
            You'll receive an email confirmation shortly. Your subscription will auto-renew 
            at $9.99 every 4 weeks after the 7-day trial. Cancel anytime.
          </p>
        </div>
      </div>
    </div>
  )
}