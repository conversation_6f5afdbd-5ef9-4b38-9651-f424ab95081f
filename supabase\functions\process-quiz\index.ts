import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { corsHeaders } from '../_shared/cors.ts';

const HEALTH_CONDITIONS = [
  {
    name: "Low Energy & Blood Sugar",
    description: "Energy support and blood sugar regulation may help improve vitality and reduce crashes",
    triggers: ["Very low or low energy levels", "Sugar cravings", "Energy crashes"]
  },
  {
    name: "Sleep Quality Issues",
    description: "Sleep support may help improve your rest quality and recovery",
    triggers: ["Trouble sleeping", "Not waking up refreshed"]
  },
  {
    name: "Joint Support Needed",
    description: "Joint health support may help reduce discomfort and improve mobility",
    triggers: ["Joint pain or stiffness", "Poor joint flexibility"]
  },
  {
    name: "Physical Performance",
    description: "Performance enhancement support for strength, stamina and physical capability",
    triggers: ["Low physical strength", "Poor stamina", "Physical weakness"]
  },
  {
    name: "Stress & Lifestyle Balance",
    description: "Stress reduction and lifestyle balance support may help improve overall wellbeing",
    triggers: ["Regular stress experience", "Poor work-life balance"]
  },
  {
    name: "Digestive Health Support",
    description: "Digestive health support may improve nutrient absorption and gut health",
    triggers: ["Poor or fair digestive health"]
  },
  {
    name: "Brain Fog & Focus",
    description: "Cognitive support may help improve mental clarity, focus and concentration",
    triggers: ["Poor concentration", "Mental fog", "Focus difficulties"]
  },
  {
    name: "Immune Support",
    description: "Immune system support may help strengthen your body's natural defenses",
    triggers: ["Getting sick frequently", "Poor nutrition", "Weak immunity"]
  },
  {
    name: "Hair & Skin Support",
    description: "Beauty and skin health support for healthy appearance and vitality",
    triggers: ["Hair thinning", "Poor hydration", "Skin issues"]
  },
  {
    name: "Respiratory & Allergy Support",
    description: "Respiratory support may help improve breathing and reduce allergy symptoms",
    triggers: ["Breathing difficulties", "Respiratory issues", "Allergy symptoms"]
  },
  {
    name: "Caffeine Dependency",
    description: "Energy regulation support to reduce caffeine dependency and improve natural energy",
    triggers: ["Regular caffeine use", "Energy dependency", "Stimulant reliance"]
  },
  {
    name: "Sedentary Lifestyle",
    description: "Active lifestyle support for those with limited physical activity",
    triggers: ["Low exercise frequency", "Sedentary work", "Physical inactivity"]
  }
];

const SUPPLEMENTS = [
  {
    name: "B-Complex Vitamins",
    condition_names: ["Low Energy & Blood Sugar", "Brain Fog & Focus", "Stress & Lifestyle Balance"],
    description: "Essential for energy metabolism and nervous system function",
    benefits: ["Supports energy production", "Reduces fatigue", "Improves mental clarity", "Supports metabolism"],
    side_effects: ["Generally well tolerated", "May cause nausea if taken on empty stomach"],
    contraindications: ["None known for healthy adults"],
    min_dose: "1 capsule",
    max_dose: "2 capsules",
    dosage: "1 capsule daily with breakfast",
    timing: "Morning with food",
    form: "Capsule",
    with_food: true,
    interactions: ["None known"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Magnesium Glycinate",
    condition_names: ["Sleep Quality Issues", "Stress & Lifestyle Balance", "Physical Performance"],
    description: "Highly absorbable form of magnesium for relaxation and sleep",
    benefits: ["Promotes relaxation", "Improves sleep quality", "Reduces muscle tension", "Supports nervous system"],
    side_effects: ["May cause loose stools in high doses"],
    contraindications: ["Kidney disease", "Heart block"],
    min_dose: "200mg",
    max_dose: "400mg",
    dosage: "200-400mg before bedtime",
    timing: "30-60 minutes before sleep",
    form: "Capsule",
    with_food: false,
    interactions: ["May enhance effects of blood pressure medications"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Omega-3 Fish Oil",
    condition_names: ["Joint Support Needed", "Brain Fog & Focus", "Stress & Lifestyle Balance"],
    description: "Anti-inflammatory support for joints and overall health",
    benefits: ["Reduces inflammation", "Supports joint mobility", "Heart health benefits", "Brain function support"],
    side_effects: ["Fishy aftertaste", "Stomach upset if taken on empty stomach"],
    contraindications: ["Fish allergies", "Blood clotting disorders"],
    min_dose: "1000mg",
    max_dose: "2000mg",
    dosage: "1000-2000mg daily with meals",
    timing: "With lunch or dinner",
    form: "Softgel",
    with_food: true,
    interactions: ["May enhance effects of blood thinners"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Vitamin D3",
    condition_names: ["Immune Support", "Sleep Quality Issues", "Stress & Lifestyle Balance"],
    description: "Essential vitamin for immune system function and overall health",
    benefits: ["Supports immune function", "Bone health", "Mood regulation", "Anti-inflammatory"],
    side_effects: ["Generally safe", "High doses may cause nausea"],
    contraindications: ["Kidney disease", "High calcium levels"],
    min_dose: "1000 IU",
    max_dose: "4000 IU",
    dosage: "1000-4000 IU daily",
    timing: "With fat-containing meal",
    form: "Softgel",
    with_food: true,
    interactions: ["May increase absorption of calcium"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Probiotics",
    condition_names: ["Digestive Health Support", "Immune Support"],
    description: "Beneficial bacteria for digestive and immune health",
    benefits: ["Supports gut health", "Improves digestion", "Boosts immune function", "Nutrient absorption"],
    side_effects: ["Initial bloating", "Gas", "Mild stomach upset"],
    contraindications: ["Severely compromised immune system"],
    min_dose: "10 billion CFU",
    max_dose: "50 billion CFU",
    dosage: "10-50 billion CFU daily",
    timing: "With or after meals",
    form: "Capsule",
    with_food: true,
    interactions: ["May reduce effectiveness of antibiotics"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  }
];

const FOOD_RECOMMENDATIONS = [
  {
    name: "Iron-Rich Foods",
    condition_names: ["Low Energy & Blood Sugar", "Brain Fog & Focus"],
    description: "Foods high in iron like spinach, lean red meat, lentils, and quinoa",
    benefits: ["Prevents anemia", "Supports oxygen transport", "Boosts energy", "Improves concentration"],
    serving_suggestions: ["Pair with vitamin C rich foods for better absorption", "Include in daily meals"]
  },
  {
    name: "Tart Cherry Juice",
    condition_names: ["Sleep Quality Issues"],
    description: "Natural source of melatonin for better sleep",
    benefits: ["Natural melatonin", "Improves sleep duration", "Reduces inflammation", "Antioxidant properties"],
    serving_suggestions: ["8oz 1-2 hours before bedtime", "Choose 100% juice without added sugar"]
  },
  {
    name: "Fatty Fish & Walnuts",
    condition_names: ["Joint Support Needed", "Brain Fog & Focus"],
    description: "Salmon, sardines, mackerel, and walnuts for omega-3 fatty acids",
    benefits: ["Anti-inflammatory omega-3s", "Joint support", "Heart healthy", "Brain food"],
    serving_suggestions: ["2-3 servings of fatty fish per week", "Handful of walnuts daily"]
  },
  {
    name: "Green Tea & Dark Chocolate",
    condition_names: ["Stress & Lifestyle Balance", "Caffeine Dependency"],
    description: "Natural stress-reducing compounds and healthy treats",
    benefits: ["L-theanine for calm", "Antioxidants", "Mood support", "Cognitive benefits"],
    serving_suggestions: ["2-3 cups green tea daily", "1oz dark chocolate (70%+ cacao)"]
  },
  {
    name: "Fermented Foods",
    condition_names: ["Digestive Health Support", "Immune Support"],
    description: "Yogurt, kefir, sauerkraut, kimchi, and kombucha",
    benefits: ["Natural probiotics", "Digestive enzymes", "Immune support", "Nutrient density"],
    serving_suggestions: ["Include 1-2 servings daily", "Start with small amounts"]
  }
];

function processQuizAnswers(answers) {
  const results = [];
  const identifiedConditions = new Set();

  // Extract all answer values
  const energyLevel = answers['550e8400-e29b-41d4-a716-446655440003'];
  const sugarCravings = answers['452ac791-288b-48aa-98ab-80d2173b2240'];
  const sleepTrouble = answers['550e8400-e29b-41d4-a716-446655440004'];
  const sleepRested = answers['598022d6-cece-4d65-a457-dcfe80a3a1fb'];
  const jointPain = answers['550e8400-e29b-41d4-a716-446655440005'];
  const jointFlexible = answers['b941ea42-0943-49e1-95a3-462f3debcc03'];
  const exerciseFreq = answers['550e8400-e29b-41d4-a716-446655440006'];
  const physicalStrong = answers['ce586653-1155-4563-839f-266623795bae'];
  const stress = answers['550e8400-e29b-41d4-a716-446655440008'];
  const workLifeBalance = answers['5b3879a5-e825-4fff-b786-b7bc1b4cc025'];
  const digestiveHealth = answers['550e8400-e29b-41d4-a716-446655440009'];
  const concentration = answers['33ddc48a-3741-428b-b877-173b0168ebf9'];
  const immunity = answers['2eb69b8e-4cbb-497d-bc90-cc2ceb10b3bc'];
  const nutrition = answers['b2254912-7bb7-4428-a365-b7c0de7c8bf5'];
  const hydration = answers['f232fa4c-4268-4d41-8e67-e0b71d67c4bd'];
  const hairHealth = answers['aeda2a7e-b897-4bc0-a67f-20f1306c83d0'];
  const breathing = answers['e2715890-51c7-4582-a89e-5007c3efb634'];
  const caffeineUse = answers['c19fca54-eef1-460c-9c2a-abb5753f39f6'];

  // Identify health conditions based on answers
  if (energyLevel === 'Very low' || energyLevel === 'Low' || sugarCravings === 'Yes') {
    identifiedConditions.add('Low Energy & Blood Sugar');
  }
  if (sleepTrouble === 'Yes' || sleepRested === 'No') {
    identifiedConditions.add('Sleep Quality Issues');
  }
  if (jointPain === 'Yes' || jointFlexible === 'No') {
    identifiedConditions.add('Joint Support Needed');
  }
  if (physicalStrong === 'No') {
    identifiedConditions.add('Physical Performance');
  }
  if (stress === 'Yes' || workLifeBalance === 'No') {
    identifiedConditions.add('Stress & Lifestyle Balance');
  }
  if (digestiveHealth === 'Poor' || digestiveHealth === 'Fair') {
    identifiedConditions.add('Digestive Health Support');
  }
  if (concentration === 'No') {
    identifiedConditions.add('Brain Fog & Focus');
  }
  if (immunity === 'No' || nutrition === 'No') {
    identifiedConditions.add('Immune Support');
  }
  if (hairHealth === 'No' || hydration === 'No') {
    identifiedConditions.add('Hair & Skin Support');
  }
  if (breathing === 'No') {
    identifiedConditions.add('Respiratory & Allergy Support');
  }
  if (caffeineUse === 'Yes') {
    identifiedConditions.add('Caffeine Dependency');
  }
  if (exerciseFreq === 'Never' || exerciseFreq === '1-2 times per week') {
    identifiedConditions.add('Sedentary Lifestyle');
  }

  // Create maps to track unique supplements and foods with multiple benefits
  const uniqueSupplements = new Map();
  const uniqueFoods = new Map();
  
  // First pass: collect all supplements and foods with their matching conditions
  identifiedConditions.forEach(conditionName => {
    const condition = HEALTH_CONDITIONS.find(c => c.name === conditionName);
    if (!condition) return;

    const supplements = SUPPLEMENTS.filter(s => s.condition_names.includes(conditionName));
    supplements.forEach(supplement => {
      if (uniqueSupplements.has(supplement.name)) {
        // Add this condition to existing entry
        const existing = uniqueSupplements.get(supplement.name);
        existing.health_tags.push(conditionName);
        existing.all_conditions.push(conditionName);
        existing.condition_count += 1;
        // Boost priority for multi-condition supplements
        existing.priority_score += 15;
      } else {
        // First time seeing this supplement
        uniqueSupplements.set(supplement.name, {
          supplement: supplement,
          health_tags: [conditionName],
          all_conditions: [conditionName],
          condition_count: 1,
          priority_score: 100,
          primary_condition: condition
        });
      }
    });

    const foods = FOOD_RECOMMENDATIONS.filter(f => f.condition_names.includes(conditionName));
    foods.forEach(food => {
      if (uniqueFoods.has(food.name)) {
        // Add this condition to existing entry
        const existing = uniqueFoods.get(food.name);
        existing.health_tags.push(conditionName);
        existing.all_conditions.push(conditionName);
        existing.condition_count += 1;
        // Boost priority for multi-condition foods
        existing.priority_score += 10;
      } else {
        // First time seeing this food
        uniqueFoods.set(food.name, {
          food: food,
          health_tags: [conditionName],
          all_conditions: [conditionName],
          condition_count: 1,
          priority_score: 80,
          primary_condition: condition
        });
      }
    });
  });

  // Second pass: create results with enhanced descriptions for multi-condition items
  uniqueSupplements.forEach((entry, supplementName) => {
    const supplement = entry.supplement;
    const isMultiCondition = entry.condition_count > 1;
    
    // Create enhanced description for multi-condition supplements
    let enhancedDescription = supplement.description;
    if (isMultiCondition) {
      enhancedDescription = `${supplement.description} **Multi-benefit supplement** - Addresses: ${entry.health_tags.join(', ')}.`;
    }

    results.push({
      health_tag_name: entry.primary_condition.name,
      health_tag_description: isMultiCondition ? 
        `Supports multiple health areas: ${entry.health_tags.join(', ')}` : 
        entry.primary_condition.description,
      recommendation_type: 'supplement',
      recommendation_id: supplement.name.toLowerCase().replace(/\s+/g, '-'),
      recommendation_name: supplement.name,
      recommendation_details: {
        description: enhancedDescription,
        benefits: supplement.benefits,
        side_effects: supplement.side_effects,
        contraindications: supplement.contraindications,
        dosage_info: {
          min_dose: supplement.min_dose,
          max_dose: supplement.max_dose,
          timing: supplement.timing,
          form: supplement.form,
          with_food: supplement.with_food
        },
        interactions: supplement.interactions,
        pregnancy_safe: supplement.pregnancy_safe,
        breastfeeding_safe: supplement.breastfeeding_safe,
        // Add multi-condition info
        all_conditions: entry.all_conditions,
        condition_count: entry.condition_count
      },
      priority_score: entry.priority_score,
      condition_count: entry.condition_count,
      all_conditions: entry.all_conditions
    });
  });

  uniqueFoods.forEach((entry, foodName) => {
    const food = entry.food;
    const isMultiCondition = entry.condition_count > 1;
    
    // Create enhanced description for multi-condition foods
    let enhancedDescription = food.description;
    if (isMultiCondition) {
      enhancedDescription = `${food.description} **Multi-benefit food** - Supports: ${entry.health_tags.join(', ')}.`;
    }

    results.push({
      health_tag_name: entry.primary_condition.name,
      health_tag_description: isMultiCondition ? 
        `Supports multiple health areas: ${entry.health_tags.join(', ')}` : 
        entry.primary_condition.description,
      recommendation_type: 'food',
      recommendation_id: food.name.toLowerCase().replace(/\s+/g, '-'),
      recommendation_name: food.name,
      recommendation_details: {
        description: enhancedDescription,
        benefits: food.benefits,
        serving_suggestions: food.serving_suggestions,
        // Add multi-condition info
        all_conditions: entry.all_conditions,
        condition_count: entry.condition_count
      },
      priority_score: entry.priority_score,
      condition_count: entry.condition_count,
      all_conditions: entry.all_conditions
    });
  });

  return results.sort((a, b) => (b.priority_score || 0) - (a.priority_score || 0));
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { answers } = await req.json();
    
    if (!answers || typeof answers !== 'object') {
      return new Response(
        JSON.stringify({ error: "Missing or invalid answers" }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    const results = processQuizAnswers(answers);
    
    return new Response(
      JSON.stringify({ 
        success: true,
        results,
        count: results.length
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});
  {
    name: "B-Complex Vitamins",
    condition_names: ["Low Energy & Blood Sugar", "Brain Fog & Focus", "Stress & Lifestyle Balance"],
    description: "Essential for energy metabolism and nervous system function",
    benefits: ["Supports energy production", "Reduces fatigue", "Improves mental clarity", "Supports metabolism"],
    side_effects: ["Generally well tolerated", "May cause nausea if taken on empty stomach"],
    contraindications: ["None known for healthy adults"],
    min_dose: "1 capsule",
    max_dose: "2 capsules",
    dosage: "1 capsule daily with breakfast",
    timing: "Morning with food",
    form: "Capsule",
    with_food: true,
    interactions: ["None known"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Magnesium Glycinate",
    condition_names: ["Sleep Quality Issues", "Stress & Lifestyle Balance", "Physical Performance"],
    description: "Highly absorbable form of magnesium for relaxation and sleep",
    benefits: ["Promotes relaxation", "Improves sleep quality", "Reduces muscle tension", "Supports nervous system"],
    side_effects: ["May cause loose stools in high doses"],
    contraindications: ["Kidney disease", "Heart block"],
    min_dose: "200mg",
    max_dose: "400mg",
    dosage: "200-400mg before bedtime",
    timing: "30-60 minutes before sleep",
    form: "Capsule",
    with_food: false,
    interactions: ["May enhance effects of blood pressure medications"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Omega-3 Fish Oil",
    condition_names: ["Joint Support Needed", "Brain Fog & Focus", "Stress & Lifestyle Balance"],
    description: "Anti-inflammatory support for joints and overall health",
    benefits: ["Reduces inflammation", "Supports joint mobility", "Heart health benefits", "Brain function support"],
    side_effects: ["Fishy aftertaste", "Stomach upset if taken on empty stomach"],
    contraindications: ["Fish allergies", "Blood clotting disorders"],
    min_dose: "1000mg",
    max_dose: "2000mg",
    dosage: "1000-2000mg daily with meals",
    timing: "With lunch or dinner",
    form: "Softgel",
    with_food: true,
    interactions: ["May enhance effects of blood thinners"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Ashwagandha",
    condition_names: ["Stress & Lifestyle Balance", "Sleep Quality Issues", "Physical Performance"],
    description: "Adaptogenic herb for stress management and cortisol balance",
    benefits: ["Reduces cortisol levels", "Improves stress response", "Supports mood", "Enhances energy"],
    side_effects: ["Drowsiness", "Stomach upset", "May lower blood sugar"],
    contraindications: ["Autoimmune disorders", "Thyroid medications"],
    min_dose: "300mg",
    max_dose: "600mg",
    dosage: "300-600mg daily",
    timing: "Morning or evening with food",
    form: "Capsule",
    with_food: true,
    interactions: ["May interact with thyroid medications"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "Probiotics",
    condition_names: ["Digestive Health Support", "Immune Support"],
    description: "Beneficial bacteria for digestive and immune health",
    benefits: ["Supports gut health", "Improves digestion", "Boosts immune function", "Nutrient absorption"],
    side_effects: ["Initial bloating", "Gas", "Mild stomach upset"],
    contraindications: ["Severely compromised immune system"],
    min_dose: "10 billion CFU",
    max_dose: "50 billion CFU",
    dosage: "10-50 billion CFU daily",
    timing: "With or after meals",
    form: "Capsule",
    with_food: true,
    interactions: ["May reduce effectiveness of antibiotics"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  // New health tag supplements
  {
    name: "Rhodiola Rosea",
    condition_names: ["Physical Performance", "Stress & Lifestyle Balance", "Low Energy & Blood Sugar"],
    description: "Adaptogenic herb that supports physical performance and stamina",
    benefits: ["Improves physical endurance", "Reduces fatigue", "Enhances stamina", "Supports recovery"],
    side_effects: ["May cause dizziness", "Restlessness in some people"],
    contraindications: ["Bipolar disorder", "Autoimmune conditions"],
    min_dose: "200mg",
    max_dose: "400mg",
    dosage: "200-400mg daily",
    timing: "Morning on empty stomach",
    form: "Capsule",
    with_food: false,
    interactions: ["May interact with diabetes medications"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "Lion's Mane Mushroom",
    condition_names: ["Brain Fog & Focus", "Stress & Lifestyle Balance"],
    description: "Medicinal mushroom that supports cognitive function and mental clarity",
    benefits: ["Improves focus", "Supports memory", "Enhances mental clarity", "Protects brain health"],
    side_effects: ["Generally well tolerated", "Rare skin rash"],
    contraindications: ["Mushroom allergies"],
    min_dose: "500mg",
    max_dose: "1000mg",
    dosage: "500-1000mg daily",
    timing: "With meals",
    form: "Capsule",
    with_food: true,
    interactions: ["None known"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "Vitamin D3",
    condition_names: ["Immune Support", "Sleep Quality Issues", "Stress & Lifestyle Balance"],
    description: "Essential vitamin for immune system function and overall health",
    benefits: ["Supports immune function", "Bone health", "Mood regulation", "Anti-inflammatory"],
    side_effects: ["Generally safe", "High doses may cause nausea"],
    contraindications: ["Kidney disease", "High calcium levels"],
    min_dose: "1000 IU",
    max_dose: "4000 IU",
    dosage: "1000-4000 IU daily",
    timing: "With fat-containing meal",
    form: "Softgel",
    with_food: true,
    interactions: ["May increase absorption of calcium"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Biotin",
    condition_names: ["Hair & Skin Support", "Low Energy & Blood Sugar"],
    description: "B-vitamin essential for healthy hair, skin, and nails",
    benefits: ["Supports hair growth", "Strengthens nails", "Improves skin health", "Supports metabolism"],
    side_effects: ["Generally well tolerated", "May affect lab tests"],
    contraindications: ["None known"],
    min_dose: "2500 mcg",
    max_dose: "10000 mcg",
    dosage: "2500-10000 mcg daily",
    timing: "With meals",
    form: "Capsule",
    with_food: true,
    interactions: ["May interfere with lab tests"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Quercetin",
    condition_names: ["Respiratory & Allergy Support", "Immune Support"],
    description: "Natural flavonoid with anti-inflammatory and antihistamine properties",
    benefits: ["Reduces allergy symptoms", "Anti-inflammatory", "Supports respiratory health", "Antioxidant"],
    side_effects: ["May cause headache", "Stomach upset in high doses"],
    contraindications: ["Blood thinning medications"],
    min_dose: "500mg",
    max_dose: "1000mg",
    dosage: "500-1000mg daily",
    timing: "With meals",
    form: "Capsule",
    with_food: true,
    interactions: ["May enhance blood thinners"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "L-Theanine",
    condition_names: "Caffeine Dependency",
    description: "Amino acid that promotes calm alertness and reduces caffeine jitters",
    benefits: ["Promotes calm alertness", "Reduces anxiety", "Improves focus", "Counters caffeine side effects"],
    side_effects: ["Generally well tolerated", "May cause drowsiness"],
    contraindications: ["Blood pressure medications"],
    min_dose: "100mg",
    max_dose: "200mg",
    dosage: "100-200mg daily",
    timing: "With or without food",
    form: "Capsule",
    with_food: false,
    interactions: ["May enhance sedative effects"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "Coenzyme Q10",
    condition_names: "Sedentary Lifestyle",
    description: "Antioxidant that supports cellular energy production and cardiovascular health",
    benefits: ["Supports cellular energy", "Heart health", "Antioxidant protection", "Exercise recovery"],
    side_effects: ["Generally well tolerated", "May cause stomach upset"],
    contraindications: ["Blood thinning medications"],
    min_dose: "100mg",
    max_dose: "200mg",
    dosage: "100-200mg daily",
    timing: "With fat-containing meal",
    form: "Softgel",
    with_food: true,
    interactions: ["May enhance blood thinners"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  // New supplements from CSV analysis
  {
    name: "Iron Bisglycinate",
    condition_names: "Brain Fog & Focus",
    description: "Highly absorbable iron for cognitive function and energy",
    benefits: ["Improves mental clarity", "Supports oxygen transport", "Reduces brain fog", "Supports hair health"],
    side_effects: ["May cause stomach upset", "Constipation in some people"],
    contraindications: ["Hemochromatosis", "Iron overload conditions"],
    min_dose: "18mg",
    max_dose: "27mg",
    dosage: "18-27mg daily with vitamin C",
    timing: "Between meals for best absorption",
    form: "Capsule",
    with_food: false,
    interactions: ["Reduces absorption of some antibiotics"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Cod Liver Oil",
    condition_names: "Immune Support",
    description: "Natural source of vitamins A, D, and omega-3 fatty acids",
    benefits: ["Immune system support", "Vision health", "Brain function", "Anti-inflammatory"],
    side_effects: ["Fishy taste", "May cause nausea"],
    contraindications: ["Fish allergies", "High vitamin A intake"],
    min_dose: "1 tsp",
    max_dose: "2 tsp",
    dosage: "1-2 teaspoons daily",
    timing: "With meals",
    form: "Liquid",
    with_food: true,
    interactions: ["May enhance blood thinners"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "Whey Protein",
    condition_names: "Physical Performance",
    description: "Complete protein for muscle building and recovery",
    benefits: ["Muscle protein synthesis", "Post-workout recovery", "Satiety", "Amino acid profile"],
    side_effects: ["Digestive upset in lactose intolerant", "Bloating"],
    contraindications: ["Milk allergies", "Severe lactose intolerance"],
    min_dose: "20g",
    max_dose: "40g",
    dosage: "20-40g post-workout",
    timing: "Within 30 minutes after exercise",
    form: "Powder",
    with_food: false,
    interactions: ["None known"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Zinc Picolinate",
    condition_names: "Hair & Skin Support",
    description: "Highly absorbable zinc for immune function and tissue repair",
    benefits: ["Hair growth support", "Skin health", "Immune function", "Wound healing"],
    side_effects: ["Stomach upset", "Metallic taste"],
    contraindications: ["Copper deficiency risk with long-term use"],
    min_dose: "15mg",
    max_dose: "30mg",
    dosage: "15-30mg daily",
    timing: "On empty stomach or with meals if upset",
    form: "Capsule",
    with_food: true,
    interactions: ["Reduces absorption of some antibiotics"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Vitamin C",
    condition_names: "Immune Support",
    description: "Essential antioxidant vitamin for immune function and collagen synthesis",
    benefits: ["Immune system support", "Antioxidant protection", "Collagen synthesis", "Iron absorption"],
    side_effects: ["Stomach upset in high doses", "Diarrhea"],
    contraindications: ["Kidney stones history", "Iron overload"],
    min_dose: "500mg",
    max_dose: "2000mg",
    dosage: "500-2000mg daily",
    timing: "With meals to reduce stomach upset",
    form: "Capsule",
    with_food: true,
    interactions: ["Enhances iron absorption"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Ceylon Cinnamon",
    condition_names: ["Low Energy & Blood Sugar", "Cardiovascular Health Support"],
    description: "Natural blood sugar support and circulation enhancement",
    benefits: ["Blood sugar regulation", "Improved circulation", "Anti-inflammatory", "Antioxidant"],
    side_effects: ["Generally well tolerated", "May lower blood sugar"],
    contraindications: ["Diabetes medications", "Blood thinners"],
    min_dose: "500mg",
    max_dose: "2000mg",
    dosage: "500-2000mg daily",
    timing: "With meals",
    form: "Capsule",
    with_food: true,
    interactions: ["May enhance diabetes medications"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "Digestive Enzymes",
    condition_names: "Digestive Health Support",
    description: "Enzyme blend to support nutrient breakdown and absorption",
    benefits: ["Improved digestion", "Reduced bloating", "Better nutrient absorption", "Digestive comfort"],
    side_effects: ["Rare allergic reactions", "Stomach upset"],
    contraindications: ["Active stomach ulcers"],
    min_dose: "1 capsule",
    max_dose: "2 capsules",
    dosage: "1-2 capsules with meals",
    timing: "Beginning of each meal",
    form: "Capsule",
    with_food: true,
    interactions: ["None known"],
    pregnancy_safe: true,
    breastfeeding_safe: true
  },
  {
    name: "Chromium Picolinate",
    condition_names: ["Low Energy & Blood Sugar", "Weight Management Support"],
    description: "Trace mineral that supports healthy blood sugar metabolism",
    benefits: ["Blood sugar regulation", "Reduced sugar cravings", "Improved insulin sensitivity", "Weight management"],
    side_effects: ["Generally well tolerated", "Rare skin reactions"],
    contraindications: ["Kidney disease", "Liver disease"],
    min_dose: "200mcg",
    max_dose: "400mcg",
    dosage: "200-400mcg daily",
    timing: "With meals",
    form: "Capsule",
    with_food: true,
    interactions: ["May enhance diabetes medications"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "Coenzyme Q10",
    condition_names: "Sedentary Lifestyle",
    description: "Antioxidant that supports cellular energy production and cardiovascular health",
    benefits: ["Supports cellular energy", "Heart health", "Antioxidant protection", "Exercise recovery"],
    side_effects: ["Generally well tolerated", "May cause stomach upset"],
    contraindications: ["Blood thinning medications"],
    min_dose: "100mg",
    max_dose: "200mg",
    dosage: "100-200mg daily",
    timing: "With fat-containing meal",
    form: "Softgel",
    with_food: true,
    interactions: ["May enhance blood thinners"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "Hawthorn Berry",
    condition_names: ["Cardiovascular Health Support"],
    description: "Traditional herbal supplement that supports cardiovascular health and circulation",
    benefits: ["Heart health support", "Circulation improvement", "Blood pressure support", "Antioxidant protection"],
    side_effects: ["Generally well tolerated", "May cause dizziness", "Stomach upset"],
    contraindications: ["Heart medications", "Blood pressure medications"],
    min_dose: "300mg",
    max_dose: "900mg",
    dosage: "300-900mg daily",
    timing: "With meals",
    form: "Capsule or extract",
    with_food: true,
    interactions: ["May enhance heart medications"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  },
  {
    name: "Green Tea Extract",
    condition_names: ["Weight Management Support"],
    description: "Concentrated green tea extract standardized for EGCG to support metabolism",
    benefits: ["Metabolism boost", "Fat oxidation", "Antioxidant support", "Energy enhancement"],
    side_effects: ["May cause jitters", "Stomach upset on empty stomach", "Sleep disruption"],
    contraindications: ["Caffeine sensitivity", "Iron deficiency"],
    min_dose: "300mg",
    max_dose: "500mg",
    dosage: "300-500mg daily",
    timing: "Between meals",
    form: "Capsule",
    with_food: false,
    interactions: ["May reduce iron absorption"],
    pregnancy_safe: false,
    breastfeeding_safe: false
  }
]

const FOOD_RECOMMENDATIONS: FoodRecommendation[] = [
  {
    name: "Iron-Rich Foods",
    condition_names: "Low Energy & Blood Sugar",
    description: "Foods high in iron like spinach, lean red meat, lentils, and quinoa",
    benefits: ["Prevents anemia", "Supports oxygen transport", "Boosts energy", "Improves concentration"],
    serving_suggestions: ["Pair with vitamin C rich foods for better absorption", "Include in daily meals"]
  },
  {
    name: "Tart Cherry Juice",
    condition_names: "Sleep Quality Issues",
    description: "Natural source of melatonin for better sleep",
    benefits: ["Natural melatonin", "Improves sleep duration", "Reduces inflammation", "Antioxidant properties"],
    serving_suggestions: ["8oz 1-2 hours before bedtime", "Choose 100% juice without added sugar"]
  },
  {
    name: "Fatty Fish & Walnuts",
    condition_names: "Joint Support Needed",
    description: "Salmon, sardines, mackerel, and walnuts for omega-3 fatty acids",
    benefits: ["Anti-inflammatory omega-3s", "Joint support", "Heart healthy", "Brain food"],
    serving_suggestions: ["2-3 servings of fatty fish per week", "Handful of walnuts daily"]
  },
  {
    name: "Green Tea & Dark Chocolate",
    condition_names: "Stress & Lifestyle Balance",
    description: "Natural stress-reducing compounds and healthy treats",
    benefits: ["L-theanine for calm", "Antioxidants", "Mood support", "Cognitive benefits"],
    serving_suggestions: ["2-3 cups green tea daily", "1oz dark chocolate (70%+ cacao)"]
  },
  {
    name: "Fermented Foods",
    condition_names: "Digestive Health Support",
    description: "Yogurt, kefir, sauerkraut, kimchi, and kombucha",
    benefits: ["Natural probiotics", "Digestive enzymes", "Immune support", "Nutrient density"],
    serving_suggestions: ["Include 1-2 servings daily", "Start with small amounts"]
  },
  // New health tag foods
  {
    name: "Protein-Rich Foods",
    condition_names: "Physical Performance",
    description: "Lean meats, eggs, Greek yogurt, legumes for muscle support and recovery",
    benefits: ["Muscle building", "Recovery support", "Sustained energy", "Metabolic boost"],
    serving_suggestions: ["Include protein with each meal", "Post-workout within 30 minutes", "0.8-1g per kg body weight"]
  },
  {
    name: "Blueberries & Nuts",
    condition_names: "Brain Fog & Focus",
    description: "Antioxidant-rich foods that support cognitive function and mental clarity",
    benefits: ["Brain health antioxidants", "Improved memory", "Enhanced focus", "Neuroprotection"],
    serving_suggestions: ["1 cup blueberries daily", "Handful of walnuts or almonds", "Add to breakfast or snacks"]
  },
  {
    name: "Citrus Fruits & Garlic",
    condition_names: "Immune Support",
    description: "Vitamin C rich foods and immune-boosting compounds",
    benefits: ["Vitamin C support", "Natural antimicrobials", "Antioxidant protection", "Immune enhancement"],
    serving_suggestions: ["1-2 citrus fruits daily", "Fresh garlic in cooking", "Include colorful vegetables"]
  },
  {
    name: "Avocados & Seeds",
    condition_names: "Hair & Skin Support",
    description: "Healthy fats and nutrients for beauty from within",
    benefits: ["Healthy fats for skin", "Vitamin E protection", "Essential fatty acids", "Nutrient density"],
    serving_suggestions: ["Half avocado daily", "Chia or flax seeds in smoothies", "Pumpkin seeds as snacks"]
  },
  {
    name: "Ginger & Turmeric",
    condition_names: "Respiratory & Allergy Support",
    description: "Anti-inflammatory spices that support respiratory health",
    benefits: ["Natural anti-inflammatory", "Respiratory support", "Antioxidant properties", "Immune modulation"],
    serving_suggestions: ["Fresh ginger tea", "Turmeric in cooking", "Golden milk before bed"]
  },
  {
    name: "Herbal Teas",
    condition_names: "Caffeine Dependency",
    description: "Caffeine-free alternatives like chamomile, peppermint, and rooibos",
    benefits: ["Natural relaxation", "Antioxidants", "Hydration", "Stress reduction"],
    serving_suggestions: ["Replace 1-2 cups coffee with herbal tea", "Evening chamomile", "Peppermint for energy"]
  },
  {
    name: "Whole Grains & Beans",
    condition_names: "Sedentary Lifestyle",
    description: "Complex carbohydrates for sustained energy and motivation to move",
    benefits: ["Sustained energy", "B-vitamins", "Fiber for health", "Stable blood sugar"],
    serving_suggestions: ["Choose whole grain options", "Include legumes in meals", "Pre-workout fuel"]
  },
  // Additional food recommendations from CSV analysis
  {
    name: "Liver & Red Meat",
    condition_names: "Brain Fog & Focus",
    description: "Iron-rich foods for cognitive function and mental clarity",
    benefits: ["High bioavailable iron", "B-vitamins for brain", "Improves oxygen transport", "Mental clarity"],
    serving_suggestions: ["2-3 servings per week", "Pair with vitamin C foods", "Choose grass-fed options"]
  },
  {
    name: "Spinach & Dark Leafy Greens",
    condition_names: "Hair & Skin Support",
    description: "Nutrient-dense greens rich in vitamins and minerals",
    benefits: ["Folate for hair growth", "Iron for circulation", "Antioxidants for skin", "Vitamin A"],
    serving_suggestions: ["2-3 cups daily", "Raw in salads or cooked", "Mix different varieties"]
  },
  {
    name: "Bone Broth",
    condition_names: "Physical Performance",
    description: "Collagen and amino acid rich broth for recovery and strength",
    benefits: ["Collagen for joints", "Amino acids for muscle", "Electrolytes", "Easy digestion"],
    serving_suggestions: ["1 cup daily", "Post-workout recovery", "Use as cooking base"]
  },
  {
    name: "Fresh Ginger Tea",
    condition_names: "Respiratory & Allergy Support",
    description: "Fresh ginger root steeped in hot water for respiratory support",
    benefits: ["Anti-inflammatory compounds", "Respiratory clearing", "Digestive support", "Circulation boost"],
    serving_suggestions: ["2-3 cups daily", "Add honey and lemon", "Fresh root preferred over powder"]
  },
  {
    name: "Ceylon Cinnamon Powder",
    condition_names: "Low Energy & Blood Sugar",
    description: "True Ceylon cinnamon powder added to foods for blood sugar support",
    benefits: ["Blood sugar regulation", "Natural sweetness", "Antioxidants", "Metabolic support"],
    serving_suggestions: ["1-2 tsp daily", "Add to oatmeal or smoothies", "Use Ceylon variety only"]
  },
  {
    name: "Fresh Elderberries",
    condition_names: "Immune Support",
    description: "Fresh or dried elderberry fruit for natural immune system support",
    benefits: ["Natural antiviral compounds", "Vitamin C", "Antioxidant anthocyanins", "Seasonal wellness"],
    serving_suggestions: ["1/2 cup fresh berries", "Elderberry syrup or tea", "Avoid raw uncooked berries"]
  },
  {
    name: "Fresh Ginger Root",
    condition_names: "Respiratory & Allergy Support",
    description: "Fresh ginger root for respiratory and digestive support",
    benefits: ["Natural anti-inflammatory", "Respiratory clearing", "Digestive comfort", "Circulation boost"],
    serving_suggestions: ["1-inch piece daily", "Grate into tea or cooking", "Add to smoothies"]
  }
]

function processQuizAnswers(answers: Record<string, string>) {
  const results: any[] = []
  const identifiedConditions = new Set<string>()

  // Analyze answers - All 12 health tags
  const energyLevel = answers['550e8400-e29b-41d4-a716-446655440003']
  const sugarCravings = answers['452ac791-288b-48aa-98ab-80d2173b2240']
  const sleepTrouble = answers['550e8400-e29b-41d4-a716-446655440004']
  const sleepRested = answers['598022d6-cece-4d65-a457-dcfe80a3a1fb']
  const jointPain = answers['550e8400-e29b-41d4-a716-446655440005']
  const jointFlexible = answers['b941ea42-0943-49e1-95a3-462f3debcc03']
  const exerciseFreq = answers['550e8400-e29b-41d4-a716-446655440006']
  const physicalStrong = answers['ce586653-1155-4563-839f-266623795bae']
  const stress = answers['550e8400-e29b-41d4-a716-446655440008']
  const workLifeBalance = answers['5b3879a5-e825-4fff-b786-b7bc1b4cc025']
  const digestiveHealth = answers['550e8400-e29b-41d4-a716-446655440009']
  const concentration = answers['33ddc48a-3741-428b-b877-173b0168ebf9']
  const immunity = answers['2eb69b8e-4cbb-497d-bc90-cc2ceb10b3bc']
  const nutrition = answers['b2254912-7bb7-4428-a365-b7c0de7c8bf5']
  const hydration = answers['f232fa4c-4268-4d41-8e67-e0b71d67c4bd']
  const hairHealth = answers['aeda2a7e-b897-4bc0-a67f-20f1306c83d0']
  const breathing = answers['e2715890-51c7-4582-a89e-5007c3efb634']
  const caffeineUse = answers['c19fca54-eef1-460c-9c2a-abb5753f39f6']
  const cardioHealth1 = answers['550e8400-e29b-41d4-a716-446655440011']
  const cardioHealth2 = answers['550e8400-e29b-41d4-a716-446655440012']
  const weightSatisfaction = answers['550e8400-e29b-41d4-a716-446655440013']
  const metabolism = answers['550e8400-e29b-41d4-a716-446655440014']

  // 1. Low Energy & Blood Sugar
  if (energyLevel === 'Very low' || energyLevel === 'Low' || sugarCravings === 'Yes') {
    identifiedConditions.add('Low Energy & Blood Sugar')
  }
  
  // 2. Sleep Quality Issues
  if (sleepTrouble === 'Yes' || sleepRested === 'No') {
    identifiedConditions.add('Sleep Quality Issues')
  }
  
  // 3. Joint Support Needed
  if (jointPain === 'Yes' || jointFlexible === 'No') {
    identifiedConditions.add('Joint Support Needed')
  }
  
  // 4. Physical Performance
  if (physicalStrong === 'No') {
    identifiedConditions.add('Physical Performance')
  }
  
  // 5. Stress & Lifestyle Balance
  if (stress === 'Yes' || workLifeBalance === 'No') {
    identifiedConditions.add('Stress & Lifestyle Balance')
  }
  
  // 6. Digestive Health Support
  if (digestiveHealth === 'Poor' || digestiveHealth === 'Fair') {
    identifiedConditions.add('Digestive Health Support')
  }
  
  // 7. Brain Fog & Focus
  if (concentration === 'No') {
    identifiedConditions.add('Brain Fog & Focus')
  }
  
  // 8. Immune Support
  if (immunity === 'No' || nutrition === 'No') {
    identifiedConditions.add('Immune Support')
  }
  
  // 9. Hair & Skin Support
  if (hairHealth === 'No' || hydration === 'No') {
    identifiedConditions.add('Hair & Skin Support')
  }
  
  // 10. Respiratory & Allergy Support
  if (breathing === 'No') {
    identifiedConditions.add('Respiratory & Allergy Support')
  }
  
  // 11. Caffeine Dependency
  if (caffeineUse === 'Yes') {
    identifiedConditions.add('Caffeine Dependency')
  }
  
  // 12. Sedentary Lifestyle
  if (exerciseFreq === 'Never' || exerciseFreq === '1-2 times per week') {
    identifiedConditions.add('Sedentary Lifestyle')
  }

  // 13. Cardiovascular Health Support
  if (cardioHealth1 === 'No' || cardioHealth2 === 'No') {
    identifiedConditions.add('Cardiovascular Health Support')
  }

  // 14. Weight Management Support
  if (weightSatisfaction === 'No' || metabolism === 'No') {
    identifiedConditions.add('Weight Management Support')
  }

  // Create maps to track unique supplements and foods with multiple benefits
  const uniqueSupplements = new Map()
  const uniqueFoods = new Map()
  
  // First pass: collect all supplements and foods with their matching conditions
  identifiedConditions.forEach(conditionName => {
    const condition = HEALTH_CONDITIONS.find(c => c.name === conditionName)
    if (!condition) return

    const supplements = SUPPLEMENTS.filter(s => s.condition_names.includes(conditionName))
    supplements.forEach(supplement => {
      if (uniqueSupplements.has(supplement.name)) {
        // Add this condition to existing entry
        const existing = uniqueSupplements.get(supplement.name)
        existing.health_tags.push(conditionName)
        existing.all_conditions.push(conditionName)
        existing.condition_count += 1
        // Boost priority for multi-condition supplements
        existing.priority_score += 15
      } else {
        // First time seeing this supplement
        uniqueSupplements.set(supplement.name, {
          supplement: supplement,
          health_tags: [conditionName],
          all_conditions: [conditionName],
          condition_count: 1,
          priority_score: 100,
          primary_condition: condition
        })
      }
    })

    const foods = FOOD_RECOMMENDATIONS.filter(f => f.condition_names.includes(conditionName))
    foods.forEach(food => {
      if (uniqueFoods.has(food.name)) {
        // Add this condition to existing entry
        const existing = uniqueFoods.get(food.name)
        existing.health_tags.push(conditionName)
        existing.all_conditions.push(conditionName)
        existing.condition_count += 1
        // Boost priority for multi-condition foods
        existing.priority_score += 10
      } else {
        // First time seeing this food
        uniqueFoods.set(food.name, {
          food: food,
          health_tags: [conditionName],
          all_conditions: [conditionName],
          condition_count: 1,
          priority_score: 80,
          primary_condition: condition
        })
      }
    })
  })

  // Second pass: create results with enhanced descriptions for multi-condition items
  uniqueSupplements.forEach((entry, supplementName) => {
    const supplement = entry.supplement
    const isMultiCondition = entry.condition_count > 1
    
    // Create enhanced description for multi-condition supplements
    let enhancedDescription = supplement.description
    if (isMultiCondition) {
      enhancedDescription = `${supplement.description} This supplement supports multiple areas: ${entry.health_tags.join(', ')}.`
    }

    results.push({
      health_tag_name: entry.primary_condition.name,
      health_tag_description: isMultiCondition ? 
        `Multi-benefit support for ${entry.health_tags.join(' + ')}` : 
        entry.primary_condition.description,
      recommendation_type: 'supplement',
      recommendation_id: supplement.name.toLowerCase().replace(/\s+/g, '-'),
      recommendation_name: supplement.name,
      recommendation_details: {
        description: enhancedDescription,
        benefits: supplement.benefits,
        side_effects: supplement.side_effects,
        contraindications: supplement.contraindications,
        dosage_info: {
          min_dose: supplement.min_dose,
          max_dose: supplement.max_dose,
          timing: supplement.timing,
          form: supplement.form,
          with_food: supplement.with_food
        },
        interactions: supplement.interactions,
        pregnancy_safe: supplement.pregnancy_safe,
        breastfeeding_safe: supplement.breastfeeding_safe,
        // Add multi-condition info
        all_conditions: entry.all_conditions,
        condition_count: entry.condition_count
      },
      priority_score: entry.priority_score,
      condition_count: entry.condition_count,
      all_conditions: entry.all_conditions
    })
  })

  uniqueFoods.forEach((entry, foodName) => {
    const food = entry.food
    const isMultiCondition = entry.condition_count > 1
    
    // Create enhanced description for multi-condition foods
    let enhancedDescription = food.description
    if (isMultiCondition) {
      enhancedDescription = `${food.description} This food supports multiple areas: ${entry.health_tags.join(', ')}.`
    }

    results.push({
      health_tag_name: entry.primary_condition.name,
      health_tag_description: isMultiCondition ? 
        `Multi-benefit support for ${entry.health_tags.join(' + ')}` : 
        entry.primary_condition.description,
      recommendation_type: 'food',
      recommendation_id: food.name.toLowerCase().replace(/\s+/g, '-'),
      recommendation_name: food.name,
      recommendation_details: {
        description: enhancedDescription,
        benefits: food.benefits,
        serving_suggestions: food.serving_suggestions,
        // Add multi-condition info
        all_conditions: entry.all_conditions,
        condition_count: entry.condition_count
      },
      priority_score: entry.priority_score,
      condition_count: entry.condition_count,
      all_conditions: entry.all_conditions
    })
  })

  return results.sort((a, b) => (b.priority_score || 0) - (a.priority_score || 0))
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { answers } = await req.json()
    
    if (!answers || typeof answers !== 'object') {
      return new Response(
        JSON.stringify({ error: "Missing or invalid answers" }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const results = processQuizAnswers(answers)
    
    return new Response(
      JSON.stringify({ 
        success: true,
        results,
        count: results.length
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})