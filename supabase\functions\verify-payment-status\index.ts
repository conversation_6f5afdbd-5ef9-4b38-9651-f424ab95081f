import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { corsHeaders } from '../_shared/cors.ts'
import Stripe from 'https://esm.sh/stripe@14.14.0?target=deno'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
  httpClient: Stripe.createFetchHttpClient(),
})

const supabase = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { session_id, customer_email } = await req.json()

    if (!session_id && !customer_email) {
      return new Response(
        JSON.stringify({ error: 'Missing session_id or customer_email' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    console.log('Verifying payment status for:', { session_id, customer_email })

    let subscriptionActive = false
    let userExists = false

    // Method 1: Check via session ID if provided
    if (session_id) {
      try {
        const session = await stripe.checkout.sessions.retrieve(session_id)
        
        if (session.subscription) {
          const subscription = await stripe.subscriptions.retrieve(session.subscription as string)
          subscriptionActive = subscription.status === 'active' || subscription.status === 'trialing'
          console.log(`Stripe subscription status: ${subscription.status}`)
        }
      } catch (error) {
        console.error('Error checking Stripe session:', error)
      }
    }

    // Method 2: Check if user exists and has subscription in our database
    if (customer_email) {
      try {
        const { data: authUser, error: authError } = await supabase.auth.admin.getUserByEmail(customer_email)
        
        if (!authError && authUser.user) {
          userExists = true
          
          const { data: subscriptions, error: subError } = await supabase
            .from('subscriptions')
            .select('*')
            .eq('user_id', authUser.user.id)
            .eq('status', 'active')
            .order('created_at', { ascending: false })
            .limit(1)
          
          if (!subError && subscriptions && subscriptions.length > 0) {
            subscriptionActive = true
            console.log('Found active subscription in database')
          }
        }
      } catch (error) {
        console.error('Error checking database:', error)
      }
    }

    return new Response(
      JSON.stringify({
        subscription_active: subscriptionActive,
        user_exists: userExists,
        timestamp: new Date().toISOString(),
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    console.error('Payment verification error:', error)
    return new Response(
      JSON.stringify({ error: 'Failed to verify payment status', details: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})