import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { corsHeaders } from '../_shared/cors.ts'
import Stripe from 'https://esm.sh/stripe@14.14.0?target=deno'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
  httpClient: Stripe.createFetchHttpClient(),
})

const supabase = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

// Payment success handler
async function handlePaymentSuccess(subscriptionObject: any) {
  try {
    console.log('Processing successful subscription:', subscriptionObject.id)
    console.log('Subscription metadata:', subscriptionObject.metadata)
    
    // Get customer details from Stripe
    const customer = await stripe.customers.retrieve(subscriptionObject.customer as string)
    const customerEmail = (customer as any).email || subscriptionObject.metadata?.customer_email
    
    if (!customerEmail) {
      console.error('No customer email found for subscription:', subscriptionObject.id)
      return
    }
    
    // Find or create user based on email
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserByEmail(customerEmail)
    
    if (authError) {
      console.error('Error finding user by email:', authError)
      return
    }
    
    if (!authUser.user) {
      console.error('No user found for email:', customerEmail)
      return
    }
    
    const userId = authUser.user.id
    
    // Create subscription record in database
    const { data: subscription, error: subError } = await supabase
      .from('subscriptions')
      .upsert({
        user_id: userId,
        stripe_id: subscriptionObject.id,
        status: 'active',
        price: subscriptionObject.items.data[0].price.unit_amount / 100,
        period_end: new Date(subscriptionObject.current_period_end * 1000).toISOString(),
      })
      .select()
    
    if (subError) {
      console.error('Error creating subscription record:', subError)
      return
    }
    
    console.log(`✅ Subscription created successfully for user ${userId}:`, subscription)
    
  } catch (error) {
    console.error('Error handling subscription success:', error)
  }
}

// Subscription cancellation handler
async function handleSubscriptionCancellation(subscriptionObject: any) {
  try {
    console.log('Processing subscription cancellation:', subscriptionObject.id)
    
    // Update subscription status in database
    const { error } = await supabase
      .from('subscriptions')
      .update({ status: 'canceled' })
      .eq('stripe_id', subscriptionObject.id)
    
    if (error) {
      console.error('Error updating subscription status:', error)
      return
    }
    
    console.log(`✅ Subscription cancelled successfully: ${subscriptionObject.id}`)
    
  } catch (error) {
    console.error('Error handling subscription cancellation:', error)
  }
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const body = await req.text()
    const signature = req.headers.get('stripe-signature')
    const webhookSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET')

    if (!signature || !webhookSecret) {
      return new Response(
        JSON.stringify({ error: 'Missing signature or webhook secret' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    let event
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret)
    } catch (err) {
      console.error('Webhook signature verification failed:', err)
      return new Response(
        JSON.stringify({ error: 'Invalid signature' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Handle the event
    switch (event.type) {
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
        const subscription = event.data.object
        console.log('Subscription created/updated:', subscription.id)
        if (subscription.status === 'active' || subscription.status === 'trialing') {
          await handlePaymentSuccess(subscription)
        }
        break
      case 'customer.subscription.deleted':
        const cancelledSubscription = event.data.object
        console.log('Subscription cancelled:', cancelledSubscription.id)
        await handleSubscriptionCancellation(cancelledSubscription)
        break
      case 'invoice.payment_succeeded':
        // Handle successful subscription renewal
        const invoice = event.data.object
        if (invoice.subscription) {
          const subscriptionObj = await stripe.subscriptions.retrieve(invoice.subscription)
          console.log('Invoice payment succeeded for subscription:', subscriptionObj.id)
          await handlePaymentSuccess(subscriptionObj)
        }
        break
      case 'invoice.payment_failed':
        // Handle failed subscription renewal
        const failedInvoice = event.data.object
        if (failedInvoice.subscription) {
          console.log('Invoice payment failed for subscription:', failedInvoice.subscription)
          // Could update subscription status to past_due here
        }
        break
      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return new Response(
      JSON.stringify({ received: true }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    console.error('Webhook error:', error)
    return new Response(
      JSON.stringify({ error: 'Webhook processing failed' }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})