import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useQuizResultsAPI, type QuizResult, type HealthTag } from '@/hooks/useQuizResultsAPI'
import { useQuizResponses } from '@/hooks/useQuizResponses'
import { useAuth } from '@/contexts/AuthContext'
import { useEmailReport } from '@/hooks/useEmailReport'
import { supabase } from '@/lib/supabase'
import { Loader2, AlertTriangle, Shield, TrendingUp, Mail, Send, Edit, Lock } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { getQuizDataFromURL } from '@/utils/quizStorage'
import { useNavigate } from 'react-router-dom'
import { checkUserSubscription } from '@/utils/subscriptionUtils'
import { HealthDisclaimer } from '@/components/HealthDisclaimer'

// Helper function to parse basic markdown formatting
function parseMarkdown(text: string) {
  const parts = text.split(/(\*\*.*?\*\*|\*.*?\*|_.*?_)/g)
  
  return parts.map((part, index) => {
    if (part.startsWith('**') && part.endsWith('**')) {
      const boldText = part.slice(2, -2)
      return <strong key={index} className="font-semibold">{boldText}</strong>
    }
    if (part.startsWith('*') && part.endsWith('*')) {
      const italicText = part.slice(1, -1)
      return <em key={index} className="italic">{italicText}</em>
    }
    if (part.startsWith('_') && part.endsWith('_')) {
      const underlineText = part.slice(1, -1)
      return <span key={index} className="underline">{underlineText}</span>
    }
    return part
  })
}

// Helper function to clean up supplement descriptions
function cleanDescription(description: string) {
  // Remove redundant "Multi-benefit supplement" and "Addresses:" parts
  let cleaned = description
    .replace(/Multi-benefit supplement\s*-?\s*/gi, '')
    .replace(/Addresses:\s*[^.]*\.?\s*/gi, '')
    .replace(/\s*-\s*$/, '') // Remove trailing dash
    .trim()
  
  // Ensure it ends with a period if it doesn't already
  if (cleaned && !cleaned.endsWith('.') && !cleaned.endsWith('!') && !cleaned.endsWith('?')) {
    cleaned += '.'
  }
  
  return cleaned
}

interface ResultsProps {
  answers: Record<string, any>
  isLoggedIn?: boolean
}


interface Recommendation {
  type: 'supplement' | 'food'
  name: string
  description: string
  benefits: string[]
  // Enhanced supplement fields
  side_effects?: string[]
  contraindications?: string[]
  dosage_info?: {
    min_dose?: string
    max_dose?: string
    form?: string
    instructions?: string
  }
  interactions?: string[]
  pregnancy_safe?: boolean
  breastfeeding_safe?: boolean
  rationale?: string
  // Food fields
  serving_suggestions?: string[]
  nutritional_info?: any
  priority?: number
  score?: number
  // Multi-condition fields
  condition_count?: number
  all_conditions?: string[]
}

// Convert database results to component format
function convertResults(results: QuizResult[]): {
  recommendations: Recommendation[]
} {
  const recommendations: Recommendation[] = []

  results.forEach(result => {
    // Add recommendation
    recommendations.push({
      type: result.recommendation_type,
      name: result.recommendation_name,
      description: result.recommendation_details.description,
      benefits: result.recommendation_details.benefits,
      side_effects: result.recommendation_details.side_effects,
      contraindications: result.recommendation_details.contraindications,
      dosage_info: result.recommendation_details.dosage_info,
      interactions: result.recommendation_details.interactions,
      pregnancy_safe: result.recommendation_details.pregnancy_safe,
      breastfeeding_safe: result.recommendation_details.breastfeeding_safe,
      rationale: result.recommendation_details.rationale,
      serving_suggestions: result.recommendation_details.serving_suggestions,
      nutritional_info: result.recommendation_details.nutritional_info,
      priority: result.priority,
      score: result.score,
      condition_count: result.condition_count || result.recommendation_details.condition_count,
      all_conditions: result.all_conditions || result.recommendation_details.all_conditions
    })
  })

  return {
    recommendations
  }
}

export function Results({ answers, isLoggedIn = false }: ResultsProps) {
  const { user } = useAuth()
  const { processResults, processing, error: processError } = useQuizResultsAPI()
  const { saveResponses, saving, error: saveError } = useQuizResponses()
  const { sendReport, sending: emailSending, error: emailError } = useEmailReport()
  const [results, setResults] = useState<QuizResult[]>([])
  const [identifiedHealthTags, setIdentifiedHealthTags] = useState<HealthTag[]>([])
  const [hasProcessed, setHasProcessed] = useState(false)
  const [reportSaved, setReportSaved] = useState(false)
  const [showEmailForm, setShowEmailForm] = useState(false)
  const [emailAddress, setEmailAddress] = useState('')
  const [emailSent, setEmailSent] = useState(false)
  const [subscriptionChecked, setSubscriptionChecked] = useState(false)
  const [hasActiveSubscription, setHasActiveSubscription] = useState(false)
  
  // State for retrieved quiz data from URL
  const [quizAnswers, setQuizAnswers] = useState<Record<string, any>>(answers || {})
  const navigate = useNavigate()

  // Check subscription status first
  useEffect(() => {
    const checkSubscription = async () => {
      if (user && !subscriptionChecked) {
        try {
          const subscriptionInfo = await checkUserSubscription(user.id)
          setHasActiveSubscription(subscriptionInfo.hasActiveSubscription)
          if (subscriptionInfo.error) {
            console.error('Subscription check error:', subscriptionInfo.error)
          }
        } catch (error) {
          console.error('Error checking subscription in Results:', error)
        } finally {
          setSubscriptionChecked(true)
        }
      } else if (!user) {
        // Non-logged in users don't have subscriptions
        setSubscriptionChecked(true)
        setHasActiveSubscription(false)
      }
    }
    
    checkSubscription()
  }, [user, subscriptionChecked])

  // Retrieve quiz data from URL if answers prop is empty
  useEffect(() => {
    if (!answers || Object.keys(answers).length === 0) {
      console.log('Results: No answers provided, trying to retrieve from URL')
      const quizDataResult = getQuizDataFromURL()
      
      if (quizDataResult.success && quizDataResult.data) {
        console.log('Results: Successfully retrieved quiz data from URL:', quizDataResult.data)
        console.log('Results: Quiz answers count:', Object.keys(quizDataResult.data.answers).length)
        setQuizAnswers(quizDataResult.data.answers)
      } else {
        console.error('Results: No quiz data found in URL, redirecting to quiz')
        navigate('/quiz')
        return
      }
    } else {
      console.log('Results: Received quiz answers via props')
      console.log('Results: Quiz answers count:', Object.keys(answers).length)
      setQuizAnswers(answers)
    }
  }, [answers, navigate])

  const handleSendEmail = async (targetEmail?: string) => {
    const emailToUse = targetEmail || emailAddress;
    
    if (!emailToUse || !emailToUse.includes('@')) {
      alert('Please enter a valid email address')
      return
    }

    try {
      const reportData = {
        healthTags: identifiedHealthTags,
        supplements: supplements,
        foods: foods,
        currentDate: currentDate
      }

      const result = await sendReport(emailToUse, reportData)
      
      if (result.success) {
        setEmailSent(true)
        setShowEmailForm(false)
        alert('Your health report has been sent successfully!')
      } else {
        alert(`Failed to send email: ${result.error}`)
      }
    } catch (error) {
      console.error('Error sending email:', error)
      alert('Failed to send email. Please try again.')
    }
  }


  const saveReportToDatabase = async (results: QuizResult[], healthTags: HealthTag[]) => {
    if (!user || reportSaved) return

    try {
      const { error } = await supabase
        .from('reports')
        .insert({
          user_id: user.id,
          quiz_responses: quizAnswers,
          health_tags: healthTags.map(tag => tag.name),
          recommendations: results.map(result => ({
            type: result.recommendation_type,
            name: result.recommendation_name,
            details: result.recommendation_details
          })),
          payment_id: 'free', // Free for logged-in users
          pdf_url: null // Will be updated when PDF is generated
        })

      if (error) {
        console.error('Error saving report:', error)
      } else {
        setReportSaved(true)
        console.log('Report saved successfully for logged-in user')
      }
    } catch (error) {
      console.error('Error saving report:', error)
    }
  }

  useEffect(() => {
    if (hasProcessed || !subscriptionChecked) return

    const handleResults = async () => {
      try {
        console.log('Starting results processing with answers:', quizAnswers)
        console.log('User subscription status:', { user: !!user, hasActiveSubscription })
        
        // Only proceed if we have quiz answers
        if (!quizAnswers || Object.keys(quizAnswers).length === 0) {
          console.log('No quiz answers available, skipping processing')
          return
        }
        
        // Check if user needs subscription for results
        if (user && !hasActiveSubscription) {
          console.log('User is logged in but has no active subscription - results require subscription')
          // Allow processing to show limited results with subscription prompts
        }
        
        // Save responses to database if user is authenticated
        if (user) {
          try {
            const savedResponses = await saveResponses(quizAnswers)
            console.log(`Quiz responses handling completed: ${savedResponses?.length || 0} responses saved`)
          } catch (err) {
            console.error('Error during response saving flow:', err)
            // Continue with processing even if saving responses fails
          }
        }
        
        // Process quiz results - subscription check will determine the depth of results
        console.log('Processing quiz results...')
        const { results: quizResults, identifiedHealthTags: healthTags } = await processResults(quizAnswers)
        console.log('Quiz results processed:', quizResults)
        console.log('Identified health tags:', healthTags)
        setResults(quizResults)
        setIdentifiedHealthTags(healthTags)
        setHasProcessed(true)

        // Save report to database for logged-in users with subscriptions
        if (user && hasActiveSubscription) {
          try {
            await saveReportToDatabase(quizResults, healthTags)
          } catch (reportErr) {
            console.error('Error saving report to database:', reportErr)
            // Continue even if report saving fails
          }
        }
      } catch (err) {
        console.error('Error handling quiz results:', err)
        // Set empty results on error so component doesn't hang
        setResults([])
        setIdentifiedHealthTags([])
        setHasProcessed(true)
      }
    }

    handleResults()
  }, [quizAnswers, user, processResults, saveResponses, hasProcessed, subscriptionChecked, hasActiveSubscription])

  // Show loading state
  if (!subscriptionChecked || !hasProcessed || processing || saving) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardContent className="flex items-center justify-center p-8">
            <div className="flex items-center space-x-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>
                {!subscriptionChecked ? 'Verifying subscription status...' : 'Processing your results...'}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Show error state
  if (processError || saveError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardContent className="p-6">
            <Alert variant="destructive">
              <AlertDescription>
                Failed to process results: {processError || saveError}
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Use the identified health tags from API instead of extracting from limited results  
  const healthTags = identifiedHealthTags
  let { recommendations } = convertResults(results)

  // Add fallback recommendations if none found
  if (results.length === 0 && hasProcessed && !processing) {
    recommendations = [
      {
        type: 'supplement',
        name: 'Daily Multivitamin',
        description: 'A comprehensive nutritional foundation',
        benefits: ['Fills nutritional gaps', 'Supports overall health', 'Immune function', 'Energy metabolism'],
        dosage_info: {
          min_dose: '1 tablet daily',
          instructions: 'Take 1 capsule with breakfast for optimal absorption and energy support.'
        },
        rationale: 'General wellness support when specific health areas are not identified'
      },
      {
        type: 'food',
        name: 'Leafy Green Vegetables',
        description: 'Nutrient-dense foundation foods',
        benefits: ['High in folate', 'Rich in iron', 'Antioxidant properties', 'Fiber for gut health'],
        serving_suggestions: ['Aim for 2-3 cups daily', 'Mix into smoothies', 'Sauté with garlic and olive oil']
      }
    ]
  }

  // Sort supplements by priority score (higher score = higher priority)
  const supplements = recommendations
    .filter(r => r.type === 'supplement')
    .sort((a, b) => (b.score || 0) - (a.score || 0))
  const foods = recommendations.filter(r => r.type === 'food')

  const currentDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })

  const SafetyIndicator = ({ supplement }: { supplement: Recommendation }) => {
    const hasWarnings = supplement.pregnancy_safe === false || supplement.breastfeeding_safe === false
    if (!hasWarnings) return null

    return (
      <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
        <div className="flex items-start space-x-2">
          <AlertTriangle className="h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-semibold text-orange-800 mb-1">Pregnancy & Breastfeeding</h4>
            <p className="text-orange-700 text-sm">
              {supplement.pregnancy_safe === false && "Not safe during pregnancy"}
              {supplement.pregnancy_safe === false && supplement.breastfeeding_safe === false && " • "}
              {supplement.breastfeeding_safe === false && "Not safe while breastfeeding"}
            </p>
          </div>
        </div>
      </div>
    )
  }




  return (
    <div id="comprehensive-health-report" className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-emerald-50">
      {/* Clean Professional Header */}
      <div className="bg-white border-b border-slate-200">
        <div className="max-w-4xl mx-auto px-6 py-8">
          <div className="text-center space-y-3">
            <h1 className="text-3xl font-bold text-slate-900">
              Your Personalized Wellness Plan
            </h1>
            <p className="text-slate-600 max-w-2xl mx-auto">
              Science-backed recommendations tailored to your unique health profile
            </p>
            <p className="text-sm text-slate-500">Generated on {currentDate}</p>
            {user && hasActiveSubscription && reportSaved && (
              <div className="mt-4 inline-flex items-center bg-emerald-50 border border-emerald-200 rounded-lg px-3 py-2">
                <span className="text-emerald-800 text-sm font-medium">✓ Report saved to your dashboard</span>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-8 space-y-12">
        {/* Subscription Status Alert */}
        {user && !hasActiveSubscription && (
          <Card className="border-orange-200 bg-orange-50">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-orange-800">
                <Lock className="h-5 w-5" />
                <span>Subscription Required for Full Results</span>
              </CardTitle>
              <CardDescription>
                You're seeing limited results. Subscribe to unlock detailed health recommendations.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-orange-100 border border-orange-300 rounded-lg p-4 mb-4">
                <p className="text-orange-800 font-medium">What you're missing:</p>
                <ul className="text-orange-700 text-sm mt-2 space-y-1">
                  <li>• Detailed supplement recommendations with dosages</li>
                  <li>• Personalized food suggestions</li>
                  <li>• Safety information and contraindications</li>
                  <li>• Complete health reports saved to your account</li>
                </ul>
              </div>
              <Button 
                onClick={() => window.location.href = '/payment'}
                className="bg-orange-600 hover:bg-orange-700 text-white"
              >
                Subscribe Now for Full Results
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Email Report Section */}
        <Card className="border-emerald-200 bg-emerald-50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-emerald-800">
              <Mail className="h-5 w-5" />
              <span>Get Your Report via Email</span>
            </CardTitle>
            <CardDescription>
              Receive your complete health report with all recommendations in your inbox
            </CardDescription>
          </CardHeader>
          <CardContent>
            {emailSent ? (
              <div className="bg-green-100 border border-green-300 rounded-lg p-4">
                <p className="text-green-800 font-medium">✅ Your health report has been sent successfully!</p>
                <p className="text-green-700 text-sm mt-1">Check your inbox (and spam folder) for your complete personalized health report.</p>
              </div>
            ) : isLoggedIn && user ? (
              <div className="space-y-4">
                <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 mb-4">
                  <p className="text-blue-800 font-medium">You are currently signed in with {user.email}</p>
                  <p className="text-blue-700 text-sm mt-1">Would you like to...</p>
                </div>
                
                {!showEmailForm ? (
                  <div className="space-y-3">
                    <Button 
                      onClick={() => {
                        if (user?.email) {
                          setEmailAddress(user.email)
                          handleSendEmail(user.email)
                        }
                      }}
                      className="w-full bg-emerald-600 hover:bg-emerald-700 text-white justify-start"
                      disabled={emailSending}
                    >
                      <Mail className="h-4 w-4 mr-2" />
                      Send the report to your account email ({user?.email})
                    </Button>
                    
                    <Button 
                      onClick={() => {
                        setShowEmailForm(true)
                        setEmailAddress('')
                      }}
                      variant="outline"
                      className="w-full justify-start"
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Send the report to a different email address
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                        Alternative Email Address
                      </Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="Enter a different email address"
                        value={emailAddress}
                        onChange={(e) => setEmailAddress(e.target.value)}
                        className="mt-1 w-full"
                      />
                    </div>
                    <div className="space-y-3">
                      <Button 
                        onClick={() => handleSendEmail()}
                        disabled={emailSending || !emailAddress || !emailAddress.includes('@')}
                        className="w-full bg-emerald-600 hover:bg-emerald-700 text-white"
                      >
                        {emailSending ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Sending...
                          </>
                        ) : (
                          <>
                            <Send className="h-4 w-4 mr-2" />
                            Send Report
                          </>
                        )}
                      </Button>
                      <Button 
                        variant="outline" 
                        onClick={() => {
                          setShowEmailForm(false)
                          setEmailAddress('')
                        }}
                        disabled={emailSending}
                        className="w-full"
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}
                
                {emailError && (
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      Failed to send email: {emailError}
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            ) : !showEmailForm ? (
              <Button 
                onClick={() => setShowEmailForm(true)}
                className="bg-emerald-600 hover:bg-emerald-700 text-white"
              >
                <Send className="h-4 w-4 mr-2" />
                Send Report to Email
              </Button>
            ) : (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                    Email Address
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your email address"
                    value={emailAddress}
                    onChange={(e) => setEmailAddress(e.target.value)}
                    className="mt-1 w-full"
                  />
                </div>
                <div className="space-y-3">
                  <Button 
                    onClick={() => handleSendEmail()}
                    disabled={emailSending || !emailAddress || !emailAddress.includes('@')}
                    className="w-full bg-emerald-600 hover:bg-emerald-700 text-white"
                  >
                    {emailSending ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        Send Report
                      </>
                    )}
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      setShowEmailForm(false)
                      setEmailAddress('')
                    }}
                    disabled={emailSending}
                    className="w-full"
                  >
                    Cancel
                  </Button>
                </div>
                {emailError && (
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      Failed to send email: {emailError}
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            )}
          </CardContent>
        </Card>
        {/* Executive Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-primary" />
              <span>Executive Summary</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-lg leading-relaxed">
              {healthTags.length > 0 
                ? `Based on your quiz responses, we identified ${healthTags.length} key health area${healthTags.length > 1 ? 's' : ''} that could benefit from targeted nutritional support. This report provides personalized supplement and food recommendations to help you optimize your health and wellbeing.`
                : 'Based on your responses, you have a solid health foundation. This report provides general wellness recommendations to help you maintain and optimize your health.'
              }
            </p>
            {healthTags.length > 0 && (
              <p className="mt-3 text-sm text-slate-600">
                Personalized for {healthTags.length} specific health goal{healthTags.length > 1 ? 's' : ''}
              </p>
            )}
          </CardContent>
        </Card>

        {/* Enhanced Health Focus Areas with Recommendations */}
        {healthTags.length > 0 && (
          <Card className="bg-white border border-slate-200 shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl text-slate-900">Your Health Focus Areas</CardTitle>
              <CardDescription>Key areas identified for targeted support with specific recommendations</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Show limited preview for non-subscribed users */}
              {!hasActiveSubscription && user && (
                <div className="text-center py-8">
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
                    <h3 className="font-semibold text-orange-800 mb-2">Subscription Required for Detailed Recommendations</h3>
                    <p className="text-orange-700 text-sm mb-4">
                      We've identified {healthTags.length} health focus areas for you, but detailed supplement and food recommendations require a subscription.
                    </p>
                    <div className="mb-4">
                      <p className="text-sm text-orange-600 font-medium">Preview of your areas:</p>
                      <div className="flex flex-wrap gap-2 mt-2 justify-center">
                        {healthTags.slice(0, 3).map((tag, index) => (
                          <span key={index} className="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-xs">
                            {tag.name}
                          </span>
                        ))}
                        {healthTags.length > 3 && (
                          <span className="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-xs">
                            +{healthTags.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                    <Button 
                      onClick={() => window.location.href = '/payment'}
                      className="bg-orange-600 hover:bg-orange-700 text-white"
                    >
                      Subscribe to See Full Recommendations
                    </Button>
                  </div>
                </div>
              )}
              
              {/* Show full content only for subscribed users */}
              {hasActiveSubscription && (
                <div>
              {healthTags.map((tag, index) => {
                // Find supplements and foods that target this health tag
                const relatedSupplements = supplements.filter(supplement =>
                  supplement.all_conditions?.some(condition =>
                    condition.toLowerCase().includes(tag.name.toLowerCase()) ||
                    tag.name.toLowerCase().includes(condition.toLowerCase())
                  )
                )

                const relatedFoods = foods.filter(food =>
                  food.all_conditions?.some(condition =>
                    condition.toLowerCase().includes(tag.name.toLowerCase()) ||
                    tag.name.toLowerCase().includes(condition.toLowerCase())
                  )
                )

                return (
                  <div key={index} className="rounded-lg p-6">
                    <div className="border-l-4 border-emerald-500 pl-4 mb-4">
                      <h3 className="font-semibold text-slate-900 mb-2">{tag.name}</h3>
                      <p className="text-slate-600 text-sm leading-relaxed">{tag.description}</p>
                    </div>

                    {/* Related Supplements */}
                    {relatedSupplements.length > 0 && (
                      <div className="mt-4">
                        <h4 className="font-medium text-slate-900 mb-3 text-sm">Recommended Supplements:</h4>
                        <div className="grid md:grid-cols-2 gap-3">
                          {relatedSupplements.map((supplement, suppIndex) => (
                            <div key={suppIndex} className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                              <div className="font-medium text-slate-900 text-sm">{supplement.name}</div>
                              {supplement.dosage_info?.form && (
                                <div className="text-xs text-slate-600 mt-1">{supplement.dosage_info.form}</div>
                              )}
                              {supplement.dosage_info?.min_dose && (
                                <div className="text-xs text-slate-600">Dose: {supplement.dosage_info.min_dose}</div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Related Foods */}
                    {relatedFoods.length > 0 && (
                      <div className="mt-4">
                        <h4 className="font-medium text-slate-900 mb-3 text-sm">Beneficial Foods:</h4>
                        <div className="grid md:grid-cols-3 gap-2">
                          {relatedFoods.map((food, foodIndex) => (
                            <div key={foodIndex} className="bg-green-50 border border-green-200 rounded-lg p-3">
                              <div className="font-medium text-slate-900 text-sm">{food.name}</div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Show message if no specific recommendations found */}
                    {relatedSupplements.length === 0 && relatedFoods.length === 0 && (
                      <div className="mt-4 text-sm text-slate-500 italic">
                        See general recommendations below that may support this health area.
                      </div>
                    )}
                  </div>
                )
              })}
                </div>
              )}
            </CardContent>
          </Card>
        )}




        {/* Clean Supplements Section - Subscription Required */}
        {supplements.length > 0 && hasActiveSubscription && (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-slate-900 mb-2">Supplement Recommendations</h2>
              <p className="text-slate-600">
                Scientifically-formulated supplements selected specifically for your health profile
              </p>
            </div>
            
            <div className="space-y-6">
              {supplements.map((supplement, index) => {
                const priorityLevel = supplement.score && supplement.score > 7 ? 'high' : supplement.score && supplement.score > 4 ? 'medium' : 'low'

                return (
                <Card key={index} className="bg-white border border-slate-200 shadow-sm">
                  <CardHeader className="pb-4">
                    <div className="flex items-start justify-between mb-3">
                      <CardTitle className="text-xl text-slate-900">
                        {supplement.name}
                      </CardTitle>
                      {priorityLevel === 'high' && (
                        <span className="text-xs font-medium px-2 py-1 rounded bg-red-100 text-red-700 ml-2 flex-shrink-0">
                          HIGH PRIORITY
                        </span>
                      )}
                    </div>

                    {supplement.dosage_info?.form && (
                      <p className="text-sm text-slate-500 mb-3">{supplement.dosage_info.form}</p>
                    )}

                    <p className="text-slate-700 leading-relaxed">
                      {parseMarkdown(cleanDescription(supplement.description))}
                    </p>
                  </CardHeader>

                  <CardContent className="pt-0 space-y-6">
                    <SafetyIndicator supplement={supplement} />

                    {/* Clean Rationale */}
                    {supplement.rationale && (
                      <div className="border-l-4 border-blue-500 pl-4 py-2">
                        <h4 className="font-semibold text-slate-900 mb-2">Why We Recommended This</h4>
                        <p className="text-slate-700 leading-relaxed">{parseMarkdown(supplement.rationale)}</p>
                      </div>
                    )}

                    {/* Clean Dosage Information */}
                    {supplement.dosage_info && (
                      <div>
                        <h4 className="font-semibold text-slate-900 mb-3">Dosage Instructions</h4>
                        <div className="grid md:grid-cols-2 gap-4">
                          {supplement.dosage_info.min_dose && (
                            <div className="border border-slate-200 rounded p-3">
                              <div className="font-medium text-slate-900 mb-1">Starting Dose</div>
                              <div className="text-slate-700">{supplement.dosage_info.min_dose}</div>
                            </div>
                          )}
                          {supplement.dosage_info.max_dose && (
                            <div className="border border-slate-200 rounded p-3">
                              <div className="font-medium text-slate-900 mb-1">Maximum Safe Dose</div>
                              <div className="text-slate-700">{supplement.dosage_info.max_dose}</div>
                            </div>
                          )}
                          {supplement.dosage_info.instructions && (
                            <div className="border border-slate-200 rounded p-3">
                              <div className="font-medium text-slate-900 mb-1">Instructions</div>
                              <div className="text-slate-700">{supplement.dosage_info.instructions}</div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Clean Benefits */}
                    <div>
                      <h4 className="font-semibold text-slate-900 mb-3">Health Benefits</h4>
                      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div className="grid md:grid-cols-2 gap-3">
                          {supplement.benefits.map((benefit, i) => (
                            <div key={i} className="flex items-start space-x-2">
                              <span className="text-green-600 mt-1">•</span>
                              <span className="text-slate-700 leading-relaxed">{parseMarkdown(benefit)}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Side Effects */}
                    {supplement.side_effects && supplement.side_effects.length > 0 && (
                      <div>
                        <h4 className="font-semibold text-slate-900 mb-3">Possible Side Effects</h4>
                        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                          <ul className="space-y-2">
                            {supplement.side_effects.map((effect, i) => (
                              <li key={i} className="flex items-start space-x-2">
                                <span className="text-amber-600 mt-1">•</span>
                                <span className="text-slate-700">{effect}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    )}

                    {/* Contraindications */}
                    {supplement.contraindications && supplement.contraindications.length > 0 && (
                      <div>
                        <h4 className="font-semibold text-slate-900 mb-3">Contraindications</h4>
                        <Alert variant="destructive">
                          <AlertTriangle className="h-4 w-4" />
                          <AlertDescription>
                            <span className="font-semibold">DO NOT TAKE IF:</span>
                            <ul className="list-disc list-inside mt-2 space-y-1">
                              {supplement.contraindications.map((item, i) => (
                                <li key={i}>{item}</li>
                              ))}
                            </ul>
                          </AlertDescription>
                        </Alert>
                      </div>
                    )}

                    {/* Interactions */}
                    {supplement.interactions && supplement.interactions.length > 0 && (
                      <div>
                        <h4 className="font-semibold text-slate-900 mb-3">Drug Interactions</h4>
                        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                          <ul className="space-y-2">
                            {supplement.interactions.map((interaction, i) => (
                              <li key={i} className="flex items-start space-x-2">
                                <span className="text-red-600 mt-1">•</span>
                                <span className="text-slate-700">{interaction}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
                )
              })}
            </div>
          </div>
        )}

        {/* Clean Food Recommendations */}
        {foods.length > 0 && (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-slate-900 mb-2">Food Recommendations</h2>
              <p className="text-slate-600">
                Nature's medicine cabinet - whole foods that naturally support your health goals
              </p>
            </div>

            <div className="space-y-6">
              {foods.map((food, index) => {
                return (
                <Card key={index} className="bg-white border border-slate-200 shadow-sm">
                  <CardHeader className="pb-4">
                    <CardTitle className="text-xl text-slate-900 mb-3">
                      {food.name}
                    </CardTitle>

                    <p className="text-slate-700 leading-relaxed">
                      {parseMarkdown(food.description)}
                    </p>
                  </CardHeader>

                  <CardContent className="pt-0 space-y-6">
                    {/* Clean Benefits */}
                    <div>
                      <h4 className="font-semibold text-slate-900 mb-3">Nutritional Benefits</h4>
                      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div className="grid md:grid-cols-2 gap-3">
                          {food.benefits.map((benefit, i) => (
                            <div key={i} className="flex items-start space-x-2">
                              <span className="text-green-600 mt-1">•</span>
                              <span className="text-slate-700 leading-relaxed">{parseMarkdown(benefit)}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Clean Serving Suggestions */}
                    {food.serving_suggestions && food.serving_suggestions.length > 0 && (
                      <div>
                        <h4 className="font-semibold text-slate-900 mb-3">How to Enjoy</h4>
                        <div className="border border-slate-200 rounded-lg p-4">
                          <div className="space-y-3">
                            {food.serving_suggestions.map((suggestion, i) => (
                              <div key={i} className="flex items-start space-x-3">
                                <span className="text-slate-600 font-medium">{i + 1}.</span>
                                <span className="text-slate-700 leading-relaxed">{suggestion}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
                )
              })}
            </div>
          </div>
        )}

        {/* Shopping List - Commented out for now */}
        {/*
        <Card className="bg-white border border-slate-200 shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl text-slate-900 flex items-center">
              <ShoppingCart className="h-5 w-5 mr-2" />
              Shopping List
            </CardTitle>
            <CardDescription>Everything you need to get started</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {supplements.length > 0 && (
              <div>
                <h3 className="font-semibold mb-4 text-slate-900 border-b border-slate-200 pb-2">Supplements</h3>
                <div className="space-y-3">
                  {supplements.map((supplement, index) => (
                    <div key={index} className="flex items-start space-x-3 p-4 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors">
                      <input type="checkbox" className="rounded mt-1" />
                      <div className="flex-1">
                        <div className="font-medium text-slate-900">
                          {supplement.name}
                          {supplement.dosage_info?.form && ` (${supplement.dosage_info.form})`}
                        </div>
                        {supplement.dosage_info?.min_dose && (
                          <div className="text-sm text-slate-600 mt-1">Recommended dose: {supplement.dosage_info.min_dose}</div>
                        )}
                        {supplement.dosage_info?.instructions && (
                          <div className="text-sm text-slate-600">{supplement.dosage_info.instructions}</div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {foods.length > 0 && (
              <div>
                <h3 className="font-semibold mb-4 text-slate-900 border-b border-slate-200 pb-2">Nutritional Foods</h3>
                <div className="space-y-3">
                  {foods.map((food, index) => (
                    <div key={index} className="flex items-center space-x-3 p-4 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors">
                      <input type="checkbox" className="rounded" />
                      <span className="flex-1 font-medium text-slate-900">{food.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
        */}



        {/* Improved Safety Guidelines */}
        <Card className="bg-white border border-slate-200 shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl text-slate-900 flex items-center">
              <Shield className="h-5 w-5 mr-2" />
              Safety Guidelines
            </CardTitle>
            <CardDescription>Your health and safety come first</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start space-x-2">
                <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-red-800 mb-1">Medical Consultation Required</h4>
                  <p className="text-red-700 text-sm">Always consult your healthcare provider before starting any supplement regimen, especially if you take medications or have health conditions.</p>
                </div>
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-slate-900 mb-4 border-b border-slate-200 pb-2">When to Stop and Consult a Doctor</h4>
                <div className="space-y-3">
                  {[
                    'Persistent nausea, headaches, or digestive issues',
                    'Unusual fatigue or energy changes',
                    'Skin rashes or allergic reactions',
                    'Changes in heart rate or blood pressure'
                  ].map((item, i) => (
                    <div key={i} className="flex items-start space-x-2 p-3 border border-slate-200 rounded-lg">
                      <span className="text-red-600 mt-1">•</span>
                      <span className="text-slate-700 text-sm">{item}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="font-semibold text-slate-900 mb-4 border-b border-slate-200 pb-2">Quality Standards to Look For</h4>
                <div className="space-y-3">
                  {[
                    'Third-party testing (USP, NSF, ConsumerLab)',
                    'GMP (Good Manufacturing Practice) certification',
                    'Clear ingredient lists (avoid proprietary blends)',
                    'Proper expiration dates and storage requirements'
                  ].map((item, i) => (
                    <div key={i} className="flex items-start space-x-2 p-3 border border-slate-200 rounded-lg">
                      <span className="text-blue-600 mt-1">•</span>
                      <span className="text-slate-700 text-sm">{item}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Merged Implementation Guide & Quick Reference */}
        <Card className="bg-white border border-slate-200 shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl text-slate-900">Implementation Guide & Quick Reference</CardTitle>
            <CardDescription>Your complete roadmap to getting started safely</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Start Slowly Warning */}
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <div className="flex items-start space-x-2">
                <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-amber-800 mb-1">Start Slowly</h4>
                  <p className="text-amber-700 text-sm">Begin with ONE supplement only. Wait 5-7 days before adding another to monitor how your body responds.</p>
                </div>
              </div>
            </div>

            {/* Priority Supplements */}
            {supplements.length > 0 && (
              <div>
                <h3 className="font-semibold mb-4 text-slate-900 border-b border-slate-200 pb-2">Your Priority Supplements</h3>
                <div className="grid md:grid-cols-2 gap-4">
                  {supplements.slice(0, 4).map((supplement, index) => (
                    <div key={index} className="border border-slate-200 rounded-lg p-4">
                      <h4 className="font-semibold text-slate-900 mb-2">{index + 1}. {supplement.name}</h4>
                      {supplement.dosage_info?.min_dose && <p className="text-sm text-slate-600 mb-1">Dose: {supplement.dosage_info.min_dose}</p>}
                      {supplement.dosage_info?.instructions && <p className="text-sm text-slate-600">{supplement.dosage_info.instructions}</p>}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Implementation Timeline */}
            <div>
              <h3 className="font-semibold mb-4 text-slate-900 border-b border-slate-200 pb-2">Implementation Timeline</h3>
              <div className="grid md:grid-cols-3 gap-4">
                <div className="border border-slate-200 rounded-lg p-4">
                  <div className="font-semibold text-slate-900 mb-2">Week 1</div>
                  <p className="text-sm text-slate-600">Start with your highest priority supplement</p>
                </div>
                <div className="border border-slate-200 rounded-lg p-4">
                  <div className="font-semibold text-slate-900 mb-2">Week 2</div>
                  <p className="text-sm text-slate-600">Add second supplement if no adverse effects</p>
                </div>
                <div className="border border-slate-200 rounded-lg p-4">
                  <div className="font-semibold text-slate-900 mb-2">Ongoing</div>
                  <p className="text-sm text-slate-600">Continue gradual introduction and monitoring</p>
                </div>
              </div>
            </div>

            {/* Daily Routine Checklist */}
            <div>
              <h3 className="font-semibold mb-4 text-slate-900 border-b border-slate-200 pb-2">Daily Routine Checklist</h3>
              <div className="grid md:grid-cols-2 gap-3">
                {[
                  'Take morning supplements with breakfast',
                  'Rate energy/mood (1-5) in journal',
                  'Eat 1-2 recommended foods',
                  'Take evening supplements (if any)',
                  'Note any changes or effects'
                ].map((item, i) => (
                  <div key={i} className="flex items-center space-x-3 p-3 border border-slate-200 rounded-lg">
                    <input type="checkbox" className="rounded" />
                    <span className="text-slate-700 text-sm">{item}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Emergency Warning */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start space-x-2">
                <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-red-800 mb-1">Emergency Contact Required</h4>
                  <p className="text-red-700 text-sm">Severe nausea, unusual heart palpitations, persistent headaches, or skin rashes</p>
                </div>
              </div>
            </div>

            {/* Key Reminder */}
            <div className="border-l-4 border-blue-500 pl-4 py-3 bg-blue-50">
              <p className="text-slate-700 font-medium text-sm">
                💡 Remember: Start with ONE supplement, monitor effects, consult healthcare providers
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Health Disclaimer */}
        <div className="mt-12">
          <HealthDisclaimer variant="prominent" />
        </div>
      </div>
    </div>
  )
}