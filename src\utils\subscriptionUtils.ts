import { supabase } from '@/lib/supabase'
import type { User } from '@supabase/supabase-js'

export interface SubscriptionInfo {
  hasActiveSubscription: boolean
  subscriptionId?: string
  stripeId?: string
  periodEnd?: string
  error?: string
}

export async function checkUserSubscription(userId: string): Promise<SubscriptionInfo> {
  try {
    const { data, error } = await supabase
      .from('subscriptions')
      .select('id, stripe_id, period_end, status')
      .eq('user_id', userId)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(1)
      .single()
    
    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error checking subscription:', error)
      return {
        hasActiveSubscription: false,
        error: error.message
      }
    }
    
    if (data) {
      return {
        hasActiveSubscription: true,
        subscriptionId: data.id,
        stripeId: data.stripe_id,
        periodEnd: data.period_end
      }
    }
    
    return { hasActiveSubscription: false }
  } catch (error) {
    console.error('Exception checking subscription:', error)
    return {
      hasActiveSubscription: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

export function isLoggedInAndSubscribed(user: User | null, subscriptionInfo?: SubscriptionInfo): boolean {
  return !!(user && subscriptionInfo?.hasActiveSubscription)
}

export async function requiresPayment(user: User | null): Promise<boolean> {
  if (!user) return true
  
  const subscriptionInfo = await checkUserSubscription(user.id)
  return !subscriptionInfo.hasActiveSubscription
}