import { useState, useEffect, useCallback } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom'
import { Landing } from '@/pages/Landing'
import { PreTest } from '@/pages/PreTest'
import { Quiz } from '@/pages/Quiz'
import { Analysis } from '@/pages/Analysis'
import { Payment } from '@/pages/Payment'
import { Results } from '@/pages/Results'
import { Dashboard } from '@/pages/Dashboard'
import { AccountCreation } from '@/pages/AccountCreation'
import { ResetPassword } from '@/pages/ResetPassword'
import { PaymentSuccess } from '@/pages/PaymentSuccess'
import { AuthProvider } from '@/contexts/AuthContext'
import { StripeProvider } from '@/contexts/StripeContext'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'
import { getQuizDataFromURL, generateURLWithQuizData } from '@/utils/quizStorage'

function AppContent() {
  const [quizAnswers, setQuizAnswers] = useState<Record<string, any>>({})
  const [userEmail, setUserEmail] = useState('')
  const [isDirectPayment, setIsDirectPayment] = useState(false)
  const { user, loading } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()

  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search)
    const directParam = searchParams.get('direct')
    if (directParam === 'payment') {
      setIsDirectPayment(true)
    }

    // Check for quiz data in URL and restore state
    const quizDataResult = getQuizDataFromURL()
    if (quizDataResult.success && quizDataResult.data) {
      console.log('Restoring quiz data from URL:', quizDataResult.data)
      setQuizAnswers(quizDataResult.data.answers)
      setUserEmail(quizDataResult.data.email)
    }

    // Check for password reset link
    const hash = window.location.hash
    if (hash) {
      const params = new URLSearchParams(hash.substring(1))
      const type = params.get('type')
      const accessToken = params.get('access_token')
      
      if (type === 'recovery' || accessToken) {
        console.log('Password reset detected, redirecting to reset page with hash')
        navigate('/reset-password' + hash)
      }
    }
    
    // Also check for auth code in query parameters (non-hash format)
    const authSearchParams = new URLSearchParams(window.location.search)
    const codeParam = authSearchParams.get('code')
    const typeParam = authSearchParams.get('type')
    
    if (codeParam && typeParam === 'recovery') {
      console.log('Password reset code detected in query params, redirecting to reset page')
      navigate(`/reset-password${window.location.search}`, { replace: true })
    }
  }, [navigate])

  // Redirect logged-in users from home to dashboard
  useEffect(() => {
    if (!loading && user && location.pathname === '/') {
      navigate('/dashboard')
    }
  }, [user, loading, location.pathname, navigate])

  const handleStartQuiz = () => {
    navigate('/pretest')
  }

  const handleStartActualQuiz = () => {
    navigate('/quiz')
  }

  const handleQuizComplete = useCallback((answers: Record<string, any>, email?: string, options?: { skipToPayment?: boolean; checkPaymentStatus?: boolean }) => {
    console.log('App: handleQuizComplete called')
    console.log('App: answers:', answers)
    console.log('App: email:', email)
    console.log('App: options:', options)
    console.log('App: current user:', user)
    
    const finalEmail = email || user?.email || ''
    setQuizAnswers(answers)
    setUserEmail(finalEmail)
    
    if (options?.skipToPayment) {
      console.log('App: Going to payment (skipToPayment)')
      const paymentURL = generateURLWithQuizData('/payment', answers, finalEmail)
      window.location.href = paymentURL
    } else if (options?.checkPaymentStatus && user) {
      console.log('App: Checking payment status')
      checkUserAccessAndRedirect(user.id, answers, finalEmail)
    } else {
      console.log('App: Going to analysis (default flow)')
      const analysisURL = generateURLWithQuizData('/analysis', answers, finalEmail)
      window.location.href = analysisURL
    }
  }, [user, navigate])

  const checkUserAccessAndRedirect = async (userId: string, answers: Record<string, any>, email: string) => {
    try {
      const { data, error } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(1)
      
      if (error) {
        console.error('Error checking user access:', error)
        const paymentURL = generateURLWithQuizData('/payment', answers, email)
        window.location.href = paymentURL
        return
      }
      
      if (data && data.length > 0) {
        const resultsURL = generateURLWithQuizData('/results', answers, email)
        window.location.href = resultsURL
      } else {
        const paymentURL = generateURLWithQuizData('/payment', answers, email)
        window.location.href = paymentURL
      }
    } catch (error) {
      console.error('Error checking user access:', error)
      const paymentURL = generateURLWithQuizData('/payment', answers, email)
      window.location.href = paymentURL
    }
  }

  const handleAnalysisComplete = async () => {
    if (user) {
      // Check if user has an active subscription before navigating to results
      try {
        const { data, error } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', user.id)
          .eq('status', 'active')
          .order('created_at', { ascending: false })
          .limit(1)
        
        if (error) {
          console.error('Error checking subscription status:', error)
          const paymentURL = generateURLWithQuizData('/payment', quizAnswers, userEmail)
          window.location.href = paymentURL
          return
        }
        
        if (data && data.length > 0) {
          console.log('User has active subscription, going to results')
          const resultsURL = generateURLWithQuizData('/results', quizAnswers, userEmail)
          window.location.href = resultsURL
        } else {
          console.log('User has no active subscription, going to payment')
          const paymentURL = generateURLWithQuizData('/payment', quizAnswers, userEmail)
          window.location.href = paymentURL
        }
      } catch (error) {
        console.error('Error checking subscription status:', error)
        const paymentURL = generateURLWithQuizData('/payment', quizAnswers, userEmail)
        window.location.href = paymentURL
      }
    } else {
      const paymentURL = generateURLWithQuizData('/payment', quizAnswers, userEmail)
      window.location.href = paymentURL
    }
  }

  const handlePaymentComplete = () => {
    const accountURL = generateURLWithQuizData('/account-creation', quizAnswers, userEmail)
    window.location.href = accountURL
  }

  const handlePaymentSuccessComplete = () => {
    const resultsURL = generateURLWithQuizData('/results', quizAnswers, userEmail)
    window.location.href = resultsURL
  }

  const handleAccountCreationComplete = () => {
    const resultsURL = generateURLWithQuizData('/results', quizAnswers, userEmail)
    window.location.href = resultsURL
  }

  const handleAccountCreationSkip = () => {
    const resultsURL = generateURLWithQuizData('/results', quizAnswers, userEmail)
    window.location.href = resultsURL
  }

  const handleBackToQuiz = () => {
    navigate('/quiz')
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <Routes>
      <Route path="/" element={user ? <Navigate to="/dashboard" replace /> : <Landing onStartQuiz={handleStartQuiz} />} />
      <Route path="/dashboard" element={
        user ? <Dashboard onStartQuiz={handleStartActualQuiz} /> : <Navigate to="/" replace />
      } />
      <Route path="/pretest" element={<PreTest onStartQuiz={handleStartActualQuiz} />} />
      <Route path="/quiz" element={
        <Quiz 
          onComplete={handleQuizComplete} 
          isDirectPayment={isDirectPayment}
          isLoggedIn={!!user}
        />
      } />
      <Route path="/analysis" element={
        <Analysis email={userEmail} answers={quizAnswers} onComplete={handleAnalysisComplete} />
      } />
      <Route path="/payment" element={
        <Payment email={userEmail} answers={quizAnswers} onPaymentComplete={handlePaymentComplete} onBack={handleBackToQuiz} />
      } />
      <Route path="/payment-success" element={
        <PaymentSuccess onContinue={handlePaymentSuccessComplete} />
      } />
      <Route path="/account-creation" element={
        <AccountCreation 
          email={userEmail} 
          answers={quizAnswers} 
          onComplete={handleAccountCreationComplete} 
          onSkip={handleAccountCreationSkip}
        />
      } />
      <Route path="/results" element={<Results answers={quizAnswers} isLoggedIn={!!user} />} />
      <Route path="/reset-password" element={
        <ResetPassword 
          onNavigateToQuiz={() => navigate('/quiz')}
          onNavigateToDashboard={() => navigate('/dashboard')}
        />
      } />
    </Routes>
  )
}

function App() {
  return (
    <AuthProvider>
      <StripeProvider>
        <Router>
          <AppContent />
        </Router>
      </StripeProvider>
    </AuthProvider>
  )
}

export default App
