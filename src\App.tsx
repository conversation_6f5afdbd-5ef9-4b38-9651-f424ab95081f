import { useState, useEffect, useCallback } from 'react'
import { BrowserRouter as Router, Routes, Route, useNavigate, useLocation } from 'react-router-dom'
import { PreTest } from '@/pages/PreTest'
import { Quiz } from '@/pages/Quiz'
import { Analysis } from '@/pages/Analysis'
import { Payment } from '@/pages/Payment'
import { Results } from '@/pages/Results'
import { Dashboard } from '@/pages/Dashboard'
import { AccountCreation } from '@/pages/AccountCreation'
import { ResetPassword } from '@/pages/ResetPassword'
import { PaymentSuccess } from '@/pages/PaymentSuccess'
import { TermsOfService } from '@/pages/TermsOfService'
import { PrivacyPolicy } from '@/pages/PrivacyPolicy'
import { AuthProvider } from '@/contexts/AuthContext'
import { StripeProvider } from '@/contexts/StripeContext'
import { useAuth } from '@/contexts/AuthContext'
import { getQuizDataFromURL, generateURLWithQuizData } from '@/utils/quizStorage'
import { checkUserSubscription } from '@/utils/subscriptionUtils'
import { SubscriptionGuard } from '@/components/SubscriptionGuard'
import { SmartRedirect } from '@/components/SmartRedirect'

function AppContent() {
  const [quizAnswers, setQuizAnswers] = useState<Record<string, any>>({})
  const [userEmail, setUserEmail] = useState('')
  const [isDirectPayment, setIsDirectPayment] = useState(false)
  const { user, loading } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()

  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search)
    const directParam = searchParams.get('direct')
    if (directParam === 'payment') {
      setIsDirectPayment(true)
    }

    // Check for quiz data in URL and restore state
    const quizDataResult = getQuizDataFromURL()
    if (quizDataResult.success && quizDataResult.data) {
      console.log('Restoring quiz data from URL:', quizDataResult.data)
      setQuizAnswers(quizDataResult.data.answers)
      setUserEmail(quizDataResult.data.email)
    }

    // Check for password reset link
    const hash = window.location.hash
    if (hash) {
      const params = new URLSearchParams(hash.substring(1))
      const type = params.get('type')
      const accessToken = params.get('access_token')
      
      if (type === 'recovery' || accessToken) {
        console.log('Password reset detected, redirecting to reset page with hash')
        navigate('/reset-password' + hash)
      }
    }
    
    // Also check for auth code in query parameters (non-hash format)
    const authSearchParams = new URLSearchParams(window.location.search)
    const codeParam = authSearchParams.get('code')
    const typeParam = authSearchParams.get('type')
    
    if (codeParam && typeParam === 'recovery') {
      console.log('Password reset code detected in query params, redirecting to reset page')
      navigate(`/reset-password${window.location.search}`, { replace: true })
    }
  }, [navigate])

  // Redirect logged-in users from home to dashboard
  useEffect(() => {
    if (!loading && user && location.pathname === '/') {
      navigate('/dashboard')
    }
  }, [user, loading, location.pathname, navigate])

  const handleStartQuiz = () => {
    navigate('/pretest')
  }

  const handleStartActualQuiz = () => {
    navigate('/quiz')
  }

  const handleQuizComplete = useCallback((answers: Record<string, any>, email?: string, options?: { skipToPayment?: boolean; checkPaymentStatus?: boolean }) => {
    console.log('App: handleQuizComplete called')
    console.log('App: answers:', answers)
    console.log('App: answers count:', Object.keys(answers).length)
    console.log('App: answers keys:', Object.keys(answers))
    console.log('App: email:', email)
    console.log('App: options:', options)
    console.log('App: current user:', user)
    
    // Add validation for empty answers
    if (Object.keys(answers).length === 0) {
      console.error('App: ERROR - Received empty answers object!')
      console.error('App: This will cause issues with results generation')
    }
    
    const finalEmail = email || user?.email || ''
    setQuizAnswers(answers)
    setUserEmail(finalEmail)
    
    if (options?.skipToPayment) {
      console.log('App: Going to payment (skipToPayment)')
      const paymentURL = generateURLWithQuizData('/payment', answers, finalEmail)
      console.log('App: Generated payment URL with quiz data')
      window.location.href = paymentURL
    } else if (options?.checkPaymentStatus && user) {
      console.log('App: Checking payment status')
      checkUserAccessAndRedirect(user.id, answers, finalEmail)
    } else {
      console.log('App: Going to analysis (default flow)')
      const analysisURL = generateURLWithQuizData('/analysis', answers, finalEmail)
      console.log('App: Generated analysis URL with quiz data')
      window.location.href = analysisURL
    }
  }, [user])

  const checkUserAccessAndRedirect = async (userId: string, answers: Record<string, any>, email: string) => {
    try {
      const subscriptionInfo = await checkUserSubscription(userId)
      
      if (subscriptionInfo.error) {
        console.error('Error checking user access:', subscriptionInfo.error)
        console.log('Database error, defaulting to payment flow')
        const paymentURL = generateURLWithQuizData('/payment', answers, email)
        window.location.href = paymentURL
        return
      }
      
      if (subscriptionInfo.hasActiveSubscription) {
        console.log('User has active subscription, going directly to results')
        const resultsURL = generateURLWithQuizData('/results', answers, email)
        window.location.href = resultsURL
      } else {
        console.log('User has no active subscription, going to payment')
        const paymentURL = generateURLWithQuizData('/payment', answers, email)
        window.location.href = paymentURL
      }
    } catch (error) {
      console.error('Error checking user access:', error)
      console.log('Exception occurred, defaulting to payment flow')
      const paymentURL = generateURLWithQuizData('/payment', answers, email)
      window.location.href = paymentURL
    }
  }

  const handleAnalysisComplete = async () => {
    if (user) {
      // Check if user has an active subscription before navigating to results
      try {
        const subscriptionInfo = await checkUserSubscription(user.id)
        
        if (subscriptionInfo.error) {
          console.error('Error checking subscription status:', subscriptionInfo.error)
          const paymentURL = generateURLWithQuizData('/payment', quizAnswers, userEmail)
          window.location.href = paymentURL
          return
        }
        
        if (subscriptionInfo.hasActiveSubscription) {
          console.log('User has active subscription, going to results')
          const resultsURL = generateURLWithQuizData('/results', quizAnswers, userEmail)
          window.location.href = resultsURL
        } else {
          console.log('User has no active subscription, going to payment')
          const paymentURL = generateURLWithQuizData('/payment', quizAnswers, userEmail)
          window.location.href = paymentURL
        }
      } catch (error) {
        console.error('Error checking subscription status:', error)
        const paymentURL = generateURLWithQuizData('/payment', quizAnswers, userEmail)
        window.location.href = paymentURL
      }
    } else {
      const paymentURL = generateURLWithQuizData('/payment', quizAnswers, userEmail)
      window.location.href = paymentURL
    }
  }

  const handlePaymentComplete = () => {
    const accountURL = generateURLWithQuizData('/account-creation', quizAnswers, userEmail)
    window.location.href = accountURL
  }

  const handlePaymentSuccessComplete = (recoveredQuizAnswers?: Record<string, any>, recoveredEmail?: string) => {
    console.log('App: handlePaymentSuccessComplete called')
    console.log('App: Current state:', { quizAnswers, userEmail })
    console.log('App: Recovered data:', { recoveredQuizAnswers, recoveredEmail })
    
    // Use recovered data if current state is empty, otherwise use current state
    const finalAnswers = (Object.keys(quizAnswers).length > 0) ? quizAnswers : (recoveredQuizAnswers || {})
    const finalEmail = userEmail || recoveredEmail || user?.email || ''
    
    console.log('App: Final data for results:', { finalAnswers, finalEmail })
    
    // Validate that we have quiz data
    if (Object.keys(finalAnswers).length === 0) {
      console.error('App: ERROR - No quiz data available for results generation!')
      console.error('App: Redirecting to quiz to start over')
      window.location.href = '/quiz'
      return
    }
    
    const resultsURL = generateURLWithQuizData('/results', finalAnswers, finalEmail)
    console.log('App: Generated results URL with quiz data')
    window.location.href = resultsURL
  }

  const handleAccountCreationComplete = () => {
    const resultsURL = generateURLWithQuizData('/results', quizAnswers, userEmail)
    window.location.href = resultsURL
  }

  const handleAccountCreationSkip = () => {
    const resultsURL = generateURLWithQuizData('/results', quizAnswers, userEmail)
    window.location.href = resultsURL
  }

  const handleBackToQuiz = () => {
    navigate('/quiz')
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <Routes>
      <Route path="/" element={<SmartRedirect onStartQuiz={handleStartQuiz} />} />
      <Route path="/dashboard" element={
        <SubscriptionGuard fallbackPath="/payment">
          <Dashboard onStartQuiz={handleStartActualQuiz} />
        </SubscriptionGuard>
      } />
      <Route path="/pretest" element={<PreTest onStartQuiz={handleStartActualQuiz} />} />
      <Route path="/quiz" element={
        <Quiz 
          onComplete={handleQuizComplete} 
          isDirectPayment={isDirectPayment}
          isLoggedIn={!!user}
        />
      } />
      <Route path="/analysis" element={
        <Analysis email={userEmail} answers={quizAnswers} onComplete={handleAnalysisComplete} />
      } />
      <Route path="/payment" element={
        <Payment email={userEmail} answers={quizAnswers} onPaymentComplete={handlePaymentComplete} onBack={handleBackToQuiz} />
      } />
      <Route path="/payment-success" element={
        <PaymentSuccess onContinue={handlePaymentSuccessComplete} />
      } />
      <Route path="/account-creation" element={
        <AccountCreation 
          email={userEmail} 
          answers={quizAnswers} 
          onComplete={handleAccountCreationComplete} 
          onSkip={handleAccountCreationSkip}
        />
      } />
      <Route path="/results" element={<Results answers={quizAnswers} isLoggedIn={!!user} />} />
      <Route path="/reset-password" element={
        <ResetPassword 
          onNavigateToQuiz={() => navigate('/quiz')}
          onNavigateToDashboard={() => navigate('/dashboard')}
        />
      } />
      <Route path="/terms" element={<TermsOfService />} />
      <Route path="/privacy" element={<PrivacyPolicy />} />
    </Routes>
  )
}

function App() {
  return (
    <AuthProvider>
      <StripeProvider>
        <Router>
          <AppContent />
        </Router>
      </StripeProvider>
    </AuthProvider>
  )
}

export default App
