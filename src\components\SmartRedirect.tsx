import { useState, useEffect } from 'react'
import { Navigate } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { checkUserSubscription } from '@/utils/subscriptionUtils'
import { Landing } from '@/pages/Landing'
import { Card, CardContent } from '@/components/ui/card'
import { Loader2 } from 'lucide-react'

interface SmartRedirectProps {
  onStartQuiz: () => void
}

export function SmartRedirect({ onStartQuiz }: SmartRedirectProps) {
  const { user, loading: authLoading } = useAuth()
  const [subscriptionLoading, setSubscriptionLoading] = useState(false)
  const [hasActiveSubscription, setHasActiveSubscription] = useState(false)

  useEffect(() => {
    const checkUserStatus = async () => {
      if (!user || authLoading) return

      console.log('🏠 SmartRedirect: Checking subscription for logged-in user:', user.id)
      setSubscriptionLoading(true)
      
      try {
        const subscriptionInfo = await checkUserSubscription(user.id)
        console.log('📊 SmartRedirect: Subscription check result:', subscriptionInfo)
        setHasActiveSubscription(subscriptionInfo.hasActiveSubscription)
      } catch (error) {
        console.error('💥 SmartRedirect: Error checking subscription:', error)
        setHasActiveSubscription(false)
      } finally {
        setSubscriptionLoading(false)
      }
    }

    checkUserStatus()
  }, [user, authLoading])

  // Show loading while checking auth or subscription
  if (authLoading || (user && subscriptionLoading)) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-teal-50 to-green-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="flex items-center justify-center p-8">
            <div className="flex items-center space-x-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Loading...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // User not logged in - show landing page
  if (!user) {
    console.log('👤 SmartRedirect: No user, showing landing page')
    return <Landing onStartQuiz={onStartQuiz} />
  }

  // User logged in with active subscription - redirect to dashboard
  if (hasActiveSubscription) {
    console.log('✅ SmartRedirect: User has subscription, redirecting to dashboard')
    return <Navigate to="/dashboard" replace />
  }

  // User logged in but no active subscription - redirect to payment
  console.log('💳 SmartRedirect: User logged in but no subscription, redirecting to payment')
  return <Navigate to="/payment" replace />
}