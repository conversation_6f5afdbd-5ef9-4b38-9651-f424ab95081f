{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/userdebuginfo.tsx", "./src/components/auth/authform.tsx", "./src/components/auth/authguard.tsx", "./src/components/payment/paymentform.tsx", "./src/components/ui/alert.tsx", "./src/components/ui/button.tsx", "./src/components/ui/card.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/contexts/authcontext.tsx", "./src/contexts/stripecontext.tsx", "./src/hooks/usecreateuserandsendreset.ts", "./src/hooks/useemailreport.ts", "./src/hooks/usemarketingsummary.ts", "./src/hooks/usequestions.ts", "./src/hooks/usequestionsstatic.ts", "./src/hooks/usequizresponses.ts", "./src/hooks/usequizresults.ts", "./src/hooks/usequizresultsapi.ts", "./src/hooks/usereportgeneration.ts", "./src/hooks/usesubscription.tsx", "./src/lib/supabase.ts", "./src/lib/utils.ts", "./src/pages/accountcreation.tsx", "./src/pages/analysis.tsx", "./src/pages/dashboard.tsx", "./src/pages/landing.tsx", "./src/pages/payment.tsx", "./src/pages/paymentsuccess.tsx", "./src/pages/pretest.tsx", "./src/pages/quiz.tsx", "./src/pages/resetpassword.tsx", "./src/pages/results.tsx", "./src/types/database.ts", "./src/types/index.ts", "./src/utils/htmltopdf.ts", "./src/utils/pdfgenerator.ts", "./src/utils/quizstorage.ts"], "version": "5.6.3"}